'use client'

import { usePathname } from 'next/navigation'
import Script from 'next/script'
import { Blog, WithContext } from 'schema-dts'

interface BlogListSEOProps {
  title: string
  description: string
}

export function BlogListSEO({ title, description }: BlogListSEOProps) {
  const pathname = usePathname()
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://revivalfiremissions.org'
  const canonicalUrl = `${baseUrl}${pathname}`
  
  // Create structured data for blog listing
  const blogSchema: WithContext<Blog> = {
    '@context': 'https://schema.org',
    '@type': 'Blog',
    headline: title,
    description: description,
    url: canonicalUrl,
    publisher: {
      '@type': 'Organization',
      name: 'Revival Fire Missions',
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/logo/logo transparent[64].png`,
      },
    },
  }

  return (
    <Script
      id="blog-list-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(blogSchema) }}
    />
  )
}
