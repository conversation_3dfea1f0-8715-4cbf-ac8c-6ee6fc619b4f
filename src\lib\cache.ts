/**
 * Simple in-memory cache implementation with expiration
 */

interface CacheItem<T> {
  data: T;
  expiry: number;
}

class Cache {
  private cache: Map<string, CacheItem<unknown>> = new Map();

  /**
   * Get an item from the cache
   * @param key The cache key
   * @returns The cached data or null if not found or expired
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key);

    // Return null if item doesn't exist
    if (!item) return null;

    // Return null if item is expired
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  /**
   * Set an item in the cache
   * @param key The cache key
   * @param data The data to cache
   * @param ttl Time to live in milliseconds
   */
  set<T>(key: string, data: T, ttl: number): void {
    const expiry = Date.now() + ttl;
    this.cache.set(key, { data, expiry });
  }

  /**
   * Remove an item from the cache
   * @param key The cache key
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all items from the cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get an item from the cache even if it's expired
   * @param key The cache key
   * @returns The cached data or null if not found
   */
  getExpired<T>(key: string): T | null {
    const item = this.cache.get(key);

    // Return null if item doesn't exist
    if (!item) return null;

    // Return the data even if it's expired
    return item.data as T;
  }

  /**
   * Check if an item exists in the cache (regardless of expiration)
   * @param key The cache key
   * @returns True if the item exists, false otherwise
   */
  has(key: string): boolean {
    return this.cache.has(key);
  }
}

// Create a singleton instance
const cache = new Cache();

export default cache;
