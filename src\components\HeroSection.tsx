"use client";

import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown, Play } from "lucide-react";
import { scrollToSection } from "@/lib/scroll";
import { cn } from "@/lib/utils";

export function HeroSection() {
  // CSS animation for the pulsing effect
  const pulseRingClass =
    "before:content-[''] before:absolute before:inset-0 before:rounded-full before:bg-red-500/70 before:animate-ping before:animate-duration-[2s]";
  const pulseClass =
    "relative overflow-visible hover:scale-105 transition-transform duration-300 ease-out";

  return (
    <section className="relative min-h-screen w-full overflow-hidden">
      <div className="absolute inset-0 w-full h-full">
        <Image
          src="/images/bg.jpg"
          alt="Church Background"
          fill
          className="object-cover translate-y-0 scale-110 transform-gpu"
          priority
        />
        <div className="absolute inset-0 bg-black/40" />
      </div>
      <div className="relative h-screen flex items-center justify-center">
        <div className="container mx-auto px-4 z-10 text-center text-white">
          <h1 className="text-5xl sm:text-7xl font-bold mb-6 animate-fade-up">
            Welcome to Revival Fire Missions
          </h1>
          <p className="text-xl sm:text-2xl mb-8 animate-fade-up animate-delay-200">
            Igniting Hearts, Transforming Lives
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <Button
              size="lg"
              className="animate-fade-up animate-delay-300 cursor-pointer"
              onClick={() => scrollToSection("contact", 80)}
            >
              Join Us Today
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="animate-fade-up animate-delay-400 cursor-pointer bg-white/10 hover:bg-white/20"
              onClick={() => scrollToSection("about", 80)}
            >
              Learn More
            </Button>
            <Button
              size="lg"
              className={cn(
                "animate-fade-up animate-delay-500 bg-red-600 hover:bg-red-700 cursor-pointer",
                pulseClass,
                pulseRingClass
              )}
              onClick={() => scrollToSection("sermons-section", 80)}
            >
              <Play className="h-4 w-4 mr-2" />
              Watch Live
            </Button>
          </div>

          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
            <button
              onClick={() => scrollToSection("about", 80)}
              className="flex flex-col items-center text-white/80 hover:text-white transition-colors"
              aria-label="Scroll down"
            >
              <span className="text-sm mb-2">Discover More</span>
              <ChevronDown className="h-6 w-6" />
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
