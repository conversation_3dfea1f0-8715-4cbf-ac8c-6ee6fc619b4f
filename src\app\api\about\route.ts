import { NextResponse } from 'next/server'
import { getClient } from '@/sanity/client'
import { getAboutQuery } from '@/sanity/queries/aboutQueries'

export const revalidate = 3600 // Revalidate every hour

export async function GET() {
  try {
    const aboutData = await getClient().fetch(getAboutQuery)
    
    if (!aboutData) {
      return NextResponse.json(
        { error: 'About data not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(aboutData)
  } catch (error) {
    console.error('Error fetching about data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch about data' },
      { status: 500 }
    )
  }
}
