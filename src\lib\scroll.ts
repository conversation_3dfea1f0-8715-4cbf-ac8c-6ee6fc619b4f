/**
 * Utility functions for smooth scrolling
 */

/**
 * Scroll to a specific section by ID with smooth animation
 * @param sectionId The ID of the section to scroll to
 * @param offset Optional offset from the top (default: 0)
 */
export const scrollToSection = (sectionId: string, offset = 0): void => {
  const element = document.getElementById(sectionId);
  
  if (element) {
    const elementPosition = element.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - offset;

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    });
  }
};

/**
 * Get the current active section based on scroll position
 * @param sectionIds Array of section IDs to check
 * @param offset Optional offset for detection (default: 100)
 * @returns The ID of the current active section
 */
export const getCurrentSection = (sectionIds: string[], offset = 100): string | null => {
  // If we're at the top of the page, return the first section
  if (window.scrollY < 100) {
    return sectionIds[0];
  }
  
  // Check each section
  for (const id of sectionIds) {
    const element = document.getElementById(id);
    if (!element) continue;
    
    const rect = element.getBoundingClientRect();
    
    // If the element is in view (with offset)
    if (rect.top <= offset && rect.bottom > offset) {
      return id;
    }
  }
  
  // If we're at the bottom of the page, return the last section
  if (window.innerHeight + window.scrollY >= document.body.offsetHeight - 100) {
    return sectionIds[sectionIds.length - 1];
  }
  
  return null;
};
