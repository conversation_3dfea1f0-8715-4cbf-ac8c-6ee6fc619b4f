{"name": "rfm-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@portabletext/react": "^3.2.1", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.0", "@sanity/icons": "^3.7.0", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^3.87.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "fast-xml-parser": "^5.2.1", "googleapis": "^148.0.0", "lucide-react": "^0.503.0", "next": "15.3.1", "next-sanity": "^9.10.6", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "sanity": "^3.87.1", "schema-dts": "^1.1.5", "styled-components": "^6.1.17", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.8", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}