import { Metadata } from "next";
import { urlForImage } from "@/sanity/image";

interface GenerateBlogMetadataParams {
  title: string;
  description?: string;
  image?: Record<string, unknown>;
  publishedAt?: string;
  author?: {
    name: string;
  };
  slug?: string;
  baseUrl?: string;
}

export function generateBlogMetadata({
  title,
  description,
  image,
  publishedAt,
  author,
  slug,
  baseUrl = process.env.NEXT_PUBLIC_BASE_URL ||
    "https://revivalfiremissions.org",
}: GenerateBlogMetadataParams): Metadata {
  // Format the date for display - can be used for UI rendering if needed
  // const formattedDate = publishedAt
  //   ? new Date(publishedAt).toLocaleDateString("en-US", {
  //       year: "numeric",
  //       month: "long",
  //       day: "numeric",
  //     })
  //   : undefined;

  // Create a clean description
  const metaDescription = description
    ? description.slice(0, 160) + (description.length > 160 ? "..." : "")
    : "Read the latest articles and updates from Revival Fire Missions.";

  // Create the canonical URL
  const canonicalUrl = slug ? `${baseUrl}/blog/${slug}` : `${baseUrl}/blog`;

  // Prepare image URL for social sharing
  const imageUrl = image
    ? urlForImage(image).width(1200).height(630).url()
    : `${baseUrl}/images/og-image.jpg`;

  // Create the metadata object
  const metadata: Metadata = {
    title: `${title} | Revival Fire Missions Blog`,
    description: metaDescription,
    authors: author
      ? [{ name: author.name }]
      : [{ name: "Revival Fire Missions" }],
    publisher: "Revival Fire Missions",
    keywords: [
      "church",
      "blog",
      "revival fire missions",
      "christian",
      "ministry",
      "lusaka",
      "zambia",
    ],
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      type: "article",
      title: title,
      description: metaDescription,
      url: canonicalUrl,
      siteName: "Revival Fire Missions",
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      publishedTime: publishedAt,
      authors: author?.name || "Revival Fire Missions",
    },
    twitter: {
      card: "summary_large_image",
      title: title,
      description: metaDescription,
      images: [imageUrl],
    },
  };

  return metadata;
}
