import { XMLParser } from "fast-xml-parser";
import type { YouTubeVideo } from "./youtube";
import cache from "./cache";

// Define a type for RSS feed entries
interface RssEntry {
  title: string;
  published: string;
  link: { "@_href": string };
  author: { name: string };
  "media:group": {
    "media:description": string;
    "media:thumbnail": { "@_url": string };
  };
}

// Cache TTL constants (in milliseconds)
const RSS_CACHE_TTL = 30 * 60 * 1000; // 30 minutes for RSS feed data

/**
 * Fetches videos from a YouTube channel's RSS feed
 * @param channelId The YouTube channel ID
 * @returns An array of video data
 */
export async function getChannelVideosFromRss(
  channelId: string
): Promise<YouTubeVideo[]> {
  try {
    // Validate input
    if (!channelId) {
      throw new Error("Channel ID is required");
    }

    // Check cache first
    const cacheKey = `rss-videos-${channelId}`;
    const cachedData = cache.get<YouTubeVideo[]>(cacheKey);

    if (cachedData) {
      console.log("Using cached RSS feed data");
      return cachedData;
    }

    console.log("Fetching fresh data from YouTube RSS feed");
    console.log("Current server time:", new Date().toISOString());

    // Fetch the RSS feed
    const rssUrl = `https://www.youtube.com/feeds/videos.xml?channel_id=${channelId}`;
    const response = await fetch(rssUrl, {
      headers: {
        Accept: "application/xml",
        "User-Agent": "Mozilla/5.0 (compatible; RFM Church Website)",
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch RSS feed: ${response.status}`);
    }

    const xml = await response.text();

    // Parse the XML
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "@_",
      isArray: (name) => name === "entry", // Always treat entry as an array
    });

    let result;
    try {
      result = parser.parse(xml);

      if (!result.feed || !result.feed.entry) {
        console.log("No entries found in RSS feed");
        return [];
      }

      console.log(
        `Found entries in RSS feed: ${result.feed.entry.length || 0}`
      );
    } catch (parseError) {
      console.error("Error parsing RSS XML:", parseError);
      return [];
    }

    // Ensure entries is always an array
    const entries = Array.isArray(result.feed.entry)
      ? result.feed.entry
      : [result.feed.entry];

    // Log the first few entries for debugging
    console.log("Processing RSS entries...");
    entries.slice(0, 3).forEach((entry: RssEntry, index: number) => {
      console.log(`RSS entry ${index + 1}: ${entry.title}`);
      console.log(`  Published: ${entry.published}`);
      console.log(`  Link: ${entry.link["@_href"]}`);

      // Calculate how long ago this was published
      const publishTime = new Date(entry.published).getTime();
      const minutesAgo = Math.round((Date.now() - publishTime) / (60 * 1000));
      console.log(`  Published ${minutesAgo} minutes ago`);
    });

    // Map RSS entries to YouTubeVideo format
    let videos = entries.map((entry: RssEntry) => {
      // Extract video ID from the link
      const videoId = entry.link["@_href"].split("v=")[1];

      // Calculate how long ago this was published
      const publishTime = new Date(entry.published).getTime();
      const minutesAgo = Math.round((Date.now() - publishTime) / (60 * 1000));

      // Determine if it's a live stream based on title and other indicators
      const titleLower = entry.title.toLowerCase();
      const descriptionLower =
        entry["media:group"]["media:description"]?.toLowerCase() || "";

      const isLive =
        titleLower.includes("live") ||
        titleLower.includes("stream") ||
        titleLower.includes("service") ||
        titleLower.includes("worship") ||
        descriptionLower.includes("live now");

      // Log potential live streams
      if (isLive) {
        console.log(`Potential live stream found in RSS: "${entry.title}"`);
        console.log(`  Published ${minutesAgo} minutes ago`);
        console.log(
          `  Live indicators: ${
            (titleLower.includes("live") ? "live " : "") +
            (titleLower.includes("stream") ? "stream " : "") +
            (titleLower.includes("service") ? "service " : "") +
            (titleLower.includes("worship") ? "worship " : "") +
            (descriptionLower.includes("live now") ? "live-now-in-desc" : "")
          }`
        );
      }

      // Get the best thumbnail available
      const thumbnailUrl = entry["media:group"]["media:thumbnail"]["@_url"];

      return {
        id: videoId,
        title: entry.title,
        description: entry["media:group"]["media:description"] || "",
        thumbnail: thumbnailUrl,
        publishedAt: entry.published,
        channelTitle: entry.author.name,
        liveBroadcastContent: isLive ? "live" : "none",
        wasLive: isLive,
      } as YouTubeVideo;
    });

    // Remove duplicate titles, keeping only the most recent one
    console.log("Checking for duplicate titles in RSS feed data...");
    const titleMap = new Map<string, YouTubeVideo>();
    const duplicateTitles = new Set<string>();

    videos.forEach((video) => {
      const existingVideo = titleMap.get(video.title);
      if (existingVideo) {
        // Found a duplicate title
        duplicateTitles.add(video.title);

        const existingDate = new Date(existingVideo.publishedAt);
        const currentDate = new Date(video.publishedAt);

        console.log(`Found duplicate title in RSS feed: "${video.title}"`);
        console.log(`  - Existing: ${existingDate.toISOString()}`);
        console.log(`  - Current:  ${currentDate.toISOString()}`);

        // Keep the most recent one
        if (currentDate > existingDate) {
          console.log(`  - Keeping the newer one`);
          titleMap.set(video.title, video);
        } else {
          console.log(`  - Keeping the existing one`);
        }
      } else {
        // First time seeing this title
        titleMap.set(video.title, video);
      }
    });

    if (duplicateTitles.size > 0) {
      console.log(`Found ${duplicateTitles.size} duplicate titles in RSS feed`);
      console.log(
        `Duplicate titles: ${Array.from(duplicateTitles).join(", ")}`
      );

      // Replace videos array with deduplicated videos
      videos = Array.from(titleMap.values());

      // Sort by publishedAt date (most recent first)
      videos.sort(
        (a, b) =>
          new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
      );

      console.log(`After removing duplicates: ${videos.length} unique videos`);
    }

    // Cache the result
    cache.set(cacheKey, videos, RSS_CACHE_TTL);

    return videos;
  } catch (error) {
    console.error("Error fetching videos from RSS feed:", error);
    return [];
  }
}

/**
 * Detects if a channel has a live stream based on RSS feed data
 * Note: This is a best-effort detection as RSS doesn't explicitly mark live streams
 * @param channelId The YouTube channel ID
 * @returns The live stream video if found, null otherwise
 */
export async function detectLiveStreamFromRss(
  channelId: string
): Promise<YouTubeVideo | null> {
  try {
    const videos = await getChannelVideosFromRss(channelId);

    // IMPORTANT: We're being much more strict about what's considered a live stream
    // to avoid false positives. We now ONLY consider a stream to be live if it has
    // very strong indicators and was published extremely recently.

    console.log(
      "Checking for live streams in RSS feed data with strict criteria..."
    );

    // Look for videos that might be live streams
    const potentialLiveStreams = videos.filter((video) => {
      // Check if it was published EXTREMELY recently (within last 15 minutes)
      // This is the most important factor - only very recent videos can be live
      const publishTime = new Date(video.publishedAt).getTime();
      const fifteenMinutesAgo = Date.now() - 15 * 60 * 1000;
      const isExtremelyRecent = publishTime > fifteenMinutesAgo;

      if (!isExtremelyRecent) {
        // If it's not extremely recent, it's not live
        return false;
      }

      // For extremely recent videos, check for STRONG live indicators
      console.log(`Found extremely recent video (< 15 min): "${video.title}"`);

      // Calculate minutes ago for logging
      const minutesAgo = Math.round((Date.now() - publishTime) / (60 * 1000));
      console.log(`  Published ${minutesAgo} minutes ago`);

      // Check for common live stream indicators in the title
      const titleLowerCase = video.title.toLowerCase();
      const descriptionLowerCase = video.description.toLowerCase();

      // ONLY accept videos with STRONG live indicators
      const hasStrongLiveKeywords =
        titleLowerCase.includes("live now") ||
        titleLowerCase.includes("streaming now") ||
        descriptionLowerCase.includes("live now") ||
        descriptionLowerCase.includes("streaming now");

      if (hasStrongLiveKeywords) {
        console.log(
          "Found STRONG live keywords in extremely recent video - considering it live:",
          video.title
        );
        return true;
      }

      // If we don't have strong keywords, it's not considered live
      console.log(
        "Recent video doesn't have strong live indicators - NOT considering it live:",
        video.title
      );
      return false;
    });

    // Sort by publish date (newest first) to prioritize the most recent live stream
    potentialLiveStreams.sort(
      (a, b) =>
        new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
    );

    return potentialLiveStreams.length > 0 ? potentialLiveStreams[0] : null;
  } catch (error) {
    console.error("Error detecting live stream from RSS:", error);
    return null;
  }
}
