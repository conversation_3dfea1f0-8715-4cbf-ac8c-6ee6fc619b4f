"use client";

import { useState, useEffect } from "react";
import { dailyScriptures } from "@/data/scriptures";

export function FloatingBible() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipping, setIsFlipping] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsFlipping(true);
      setTimeout(() => {
        setCurrentIndex((prevIndex) =>
          prevIndex === dailyScriptures.length - 1 ? 0 : prevIndex + 1
        );
        setIsFlipping(false);
      }, 500); // Half of the animation duration
    }, 10000); // Change scripture every 10 seconds

    return () => clearInterval(interval);
  }, []);

  const currentScripture = dailyScriptures[currentIndex];

  return (
    <section className="relative -mt-32 mb-32 z-20">
      <div className="container mx-auto px-4">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 transform hover:-translate-y-2 transition-transform duration-300">
          <div className="flex flex-col items-center text-center">
            <h2 className="text-3xl font-bold mb-4 text-primary">
              Daily Scripture
            </h2>
            <div
              className={`transition-all duration-1000 ${
                isFlipping
                  ? "opacity-0 transform -translate-y-4"
                  : "opacity-100 transform translate-y-0"
              }`}
            >
              <p className="text-xl italic mb-4">
                &quot;{currentScripture.verse}&quot;
              </p>
              <span className="text-primary font-semibold">
                - {currentScripture.reference}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
