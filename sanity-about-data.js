/**
 * This is a sample script to help you populate the About section in Sanity Studio.
 * You can run this script in the Sanity Studio Vision tab to create the initial About document.
 * 
 * Instructions:
 * 1. Go to your Sanity Studio (http://localhost:3000/studio)
 * 2. Click on the "Vision" tab
 * 3. Paste this script and run it
 * 4. After running, you can edit the About document in the Content tab
 */

// Create a new About document
const doc = {
  _type: 'about',
  title: 'About Revival Fire Missions',
  mainContent: "Revival Fire Missions is a dynamic church dedicated to spreading the Gospel and transforming lives through the power of God's love. Founded on the principles of faith, hope, and love, we strive to create a welcoming community where everyone can experience God's presence.",
  secondaryContent: "Our mission is to ignite hearts and transform lives by sharing the message of Jesus Christ. We believe in the power of prayer, worship, and community to bring about lasting change in individuals and society.",
  values: [
    "Faith in Action",
    "Compassionate Community",
    "Biblical Teaching",
    "Spirit-Led Worship"
  ],
  primaryButtonText: "Our Vision",
  secondaryButtonText: "Learn More"
}

// Note: You'll need to upload images in the Sanity Studio interface
// The slideImages field will be empty initially and you'll need to add them manually

// Create the document
createOrReplace(doc)
