# Live Stream Archive System

## Overview

The Live Stream Archive System automatically saves ended live streams and makes them accessible for users to watch later as replays on your platform. This ensures that viewers who missed the live broadcast can still access the content.

## How It Works

### 1. **Automatic Detection**
- The real-time monitoring system detects when a live stream ends
- Immediately triggers the archiving process
- No manual intervention required

### 2. **Archiving Process**
- Verifies the stream has actually ended (has both start and end times)
- Calculates stream duration and collects analytics
- Determines processing status (YouTube needs time to process recordings)
- Estimates when the replay will be available

### 3. **User Notifications**
- Shows "Saving Live Stream" notification when archiving starts
- Shows "Replay Available" notification when ready for viewing
- Automatically refreshes the sermon list to include the replay

### 4. **Replay Access**
- Archived streams appear in the regular sermon list
- Marked as past live streams with special indicators
- Full video player functionality with no external suggestions

## System Components

### Core Files

#### `src/lib/live-stream-archive.ts`
- **`archiveEndedLiveStream()`** - Main archiving function
- **`isReplayReady()`** - Checks if replay is available
- **`getArchivedLiveStreams()`** - Retrieves all archived streams

#### `src/lib/youtube-realtime.ts` (Enhanced)
- **`startArchivingProcess()`** - Triggers archiving when stream ends
- **`scheduleReplayReadyCheck()`** - Monitors processing status
- Enhanced event system for archive notifications

#### `src/app/api/archived-streams/route.ts`
- **GET** - Retrieves archived streams with pagination
- **POST** - Manual archiving for specific videos

#### `src/components/SermonSection.tsx` (Enhanced)
- Archive processing notifications
- Replay ready notifications
- Real-time event handling

## Features

### ✅ **Automatic Archiving**
- **Trigger**: Stream end detection
- **Process**: Immediate archiving with analytics
- **Verification**: Ensures video belongs to your channel
- **Status Tracking**: Monitors YouTube processing

### ✅ **Smart Processing Detection**
- **Processing Status**: Tracks YouTube's video processing
- **Time Estimates**: Provides realistic processing time estimates
- **Ready Notifications**: Alerts when replay becomes available
- **Periodic Checks**: Monitors status every 30 minutes

### ✅ **User Experience**
- **Immediate Feedback**: Shows archiving progress
- **Clear Notifications**: Informs users about replay availability
- **Seamless Integration**: Replays appear in regular sermon list
- **No External Suggestions**: Uses enhanced embed parameters

### ✅ **Analytics & Metadata**
- **Stream Duration**: Calculated from start/end times
- **Viewer Statistics**: Peak and average viewers
- **Processing Time**: Tracks how long YouTube takes
- **Archive Timestamp**: When archiving was completed

## Processing Timeline

### Immediate (0-5 minutes)
- ✅ Stream end detected
- ✅ Archiving process started
- ✅ Basic metadata collected
- ✅ "Saving Live Stream" notification shown

### Short Term (30 minutes - 2 hours)
- 🔄 YouTube processes the recording
- 🔄 Periodic status checks every 30 minutes
- 🔄 Processing status updates

### Ready (2-6 hours typically)
- ✅ YouTube processing complete
- ✅ "Replay Available" notification shown
- ✅ Video appears in sermon list
- ✅ Full replay functionality available

## API Endpoints

### GET `/api/archived-streams`
Retrieves archived live streams with pagination and filtering.

**Parameters:**
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20)
- `ready` - Only show ready replays (true/false)

**Response:**
```json
{
  "archivedStreams": [...],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 100,
    "hasNextPage": true
  },
  "statistics": {
    "totalArchived": 100,
    "totalReady": 95,
    "totalProcessing": 3,
    "totalFailed": 2
  }
}
```

### POST `/api/archived-streams`
Manually archive a specific video (for retroactive archiving).

**Body:**
```json
{
  "videoId": "VIDEO_ID",
  "forceReprocess": false
}
```

## User Interface

### Archive Processing Notification
```
📼 Saving Live Stream
"Sunday Service - December 10, 2024" is being processed and will be available for replay soon.
[X]
```

### Replay Ready Notification
```
🎬 Replay Available
"Sunday Service - December 10, 2024" is now available for replay below.
[X]
```

### Sermon List Integration
- Archived streams appear with other sermons
- Special "REPLAY" badge for past live streams
- Stream duration and viewer count displayed
- Original live date preserved

## Configuration

### Environment Variables
```env
YOUTUBE_API_KEY=your_api_key
YOUTUBE_CHANNEL_ID=your_channel_id
```

### Processing Settings
- **Check Interval**: 30 minutes
- **Max Checks**: 24 (12 hours total)
- **Processing Estimates**:
  - Short streams (≤30 min): ~1 hour
  - Medium streams (≤2 hours): ~3 hours
  - Long streams (>2 hours): ~6 hours

## Monitoring & Debugging

### Console Logs
- `📼 Starting archive process for: "Stream Title"`
- `✅ Stream archived successfully: "Stream Title"`
- `🎬 Replay is now ready for: "Stream Title"`
- `⏰ Scheduled replay ready checks for: "Stream Title"`

### Error Handling
- API failures are logged and retried
- Processing failures are tracked
- Users are notified of any issues
- Fallback to manual archiving available

## Benefits

### For Your Church
- ✅ **No Lost Content** - Every live stream is automatically saved
- ✅ **Increased Reach** - People can watch replays anytime
- ✅ **Better Engagement** - Viewers don't miss important messages
- ✅ **Professional Experience** - Seamless archiving process

### For Your Viewers
- ✅ **Always Available** - Can watch missed live streams
- ✅ **Clear Notifications** - Know when replays are ready
- ✅ **Easy Access** - Replays appear with other sermons
- ✅ **Quality Experience** - No external video suggestions

## Future Enhancements

### Planned Features
- 📋 **Automatic Timestamps** - Chapter markers for long streams
- 📋 **Highlight Clips** - Auto-generate short clips from streams
- 📋 **Download Options** - Allow offline viewing
- 📋 **Email Notifications** - Notify subscribers when replays are ready

---

**Status**: ✅ **IMPLEMENTED** - Live streams are now automatically archived and made available as replays with full user notifications and seamless integration.
