import { groq } from 'next-sanity'

// Get all posts
export const postsQuery = groq`
  *[_type == "post"] | order(publishedAt desc) {
    _id,
    title,
    slug,
    mainImage,
    publishedAt,
    "categories": categories[]->title,
    "author": author->{name, image},
    "estimatedReadingTime": round(length(pt::text(body)) / 5 / 180)
  }
`

// Get a single post by slug
export const postBySlugQuery = groq`
  *[_type == "post" && slug.current == $slug][0] {
    _id,
    title,
    slug,
    mainImage,
    body,
    publishedAt,
    "categories": categories[]->title,
    "author": author->{name, image},
    "estimatedReadingTime": round(length(pt::text(body)) / 5 / 180)
  }
`

// Get all categories
export const categoriesQuery = groq`
  *[_type == "category"] {
    _id,
    title,
    description
  }
`

// Get posts by category
export const postsByCategoryQuery = groq`
  *[_type == "post" && $category in categories[]->title] | order(publishedAt desc) {
    _id,
    title,
    slug,
    mainImage,
    publishedAt,
    "categories": categories[]->title,
    "author": author->{name, image},
    "estimatedReadingTime": round(length(pt::text(body)) / 5 / 180)
  }
`
