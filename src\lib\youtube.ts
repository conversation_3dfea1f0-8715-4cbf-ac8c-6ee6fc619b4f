import { google } from "googleapis";
import cache from "./cache";

const youtube = google.youtube("v3");

// Initialize with your YouTube API key
const apiKey = process.env.YOUTUBE_API_KEY;

// Cache TTL constants (in milliseconds)
const LIVE_STREAM_CACHE_TTL = 2 * 60 * 1000; // 2 minutes for live streams (reduced to ensure fresh data)
const SERMONS_CACHE_TTL = 30 * 60 * 1000; // 30 minutes for regular sermons (reduced to ensure we get recent videos)

export interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  publishedAt: string;
  channelTitle: string;
  liveBroadcastContent: "live" | "upcoming" | "none";
  wasLive?: boolean;
  verifiedLive?: boolean; // Flag to indicate if this is a verified live stream
}

export async function getChannelLiveStreams(
  channelId: string,
  forceRefresh: boolean = false
): Promise<YouTubeVideo[]> {
  try {
    // Validate inputs
    if (!channelId) {
      throw new Error("Channel ID is required");
    }

    if (!apiKey) {
      throw new Error("YouTube API key is not configured");
    }

    // Check cache first (unless force refresh is requested)
    const cacheKey = `live-streams-${channelId}`;

    if (forceRefresh) {
      console.log("Force refresh requested, bypassing cache");
      // Explicitly delete the cache entry
      cache.delete(cacheKey);
    } else {
      const cachedData = cache.get<YouTubeVideo[]>(cacheKey);
      if (cachedData) {
        console.log("Using cached live streams data");
        return cachedData;
      }
    }

    console.log("Fetching fresh live streams data from YouTube API");
    // First try to get currently live streams using the most accurate method
    console.log("Fetching live streams directly from YouTube API");
    const response = await youtube.search.list({
      key: apiKey,
      part: ["snippet", "id"], // Include id part for more details
      channelId,
      eventType: "live", // This is the most important filter - only truly live streams
      type: ["video"],
      maxResults: 3, // Reduced to save quota and because we only need truly live streams
      order: "date",
    });

    // If we found live streams, double-check them with a videos.list call
    // This gives us more accurate information about their actual live status
    if (response.data.items && response.data.items.length > 0) {
      console.log(
        "Found potential live streams, verifying with videos.list API"
      );

      // Log the potential live streams for debugging
      response.data.items.forEach((item, index) => {
        const publishTime = new Date(item.snippet?.publishedAt || "").getTime();
        const minutesAgo = Math.round((Date.now() - publishTime) / (60 * 1000));
        console.log(
          `Potential live stream ${index + 1}: "${item.snippet?.title}" (${minutesAgo} minutes ago, ${item.snippet?.liveBroadcastContent})`
        );
      });

      // Extract video IDs
      const videoIds = response.data.items
        .map((item) => item.id?.videoId)
        .filter(Boolean);

      if (videoIds.length > 0) {
        try {
          // Get detailed information about these videos
          const videoDetails = await youtube.videos.list({
            key: apiKey,
            part: ["snippet", "liveStreamingDetails", "status"],
            id: videoIds as string[],
          });

          // Filter to only include videos that are actually live
          const trulyLiveVideos = videoDetails.data.items?.filter((video) => {
            // CRITICAL: First check if this video belongs to our channel
            const videoChannelId = video.snippet?.channelId;
            const belongsToOurChannel = videoChannelId === channelId;

            if (!belongsToOurChannel) {
              console.log(
                `❌ REJECTING live video "${video.snippet?.title}" - belongs to different channel: ${videoChannelId} (expected: ${channelId})`
              );
              return false;
            }

            // Check if video is deleted or private
            if (
              video.status?.privacyStatus === "private" ||
              video.status?.privacyStatus === "unlisted" ||
              video.status?.uploadStatus === "deleted"
            ) {
              console.log(
                `Skipping deleted/private video: ${video.snippet?.title}`
              );
              return false;
            }

            // Check for actual live broadcast status
            const isMarkedLive = video.snippet?.liveBroadcastContent === "live";

            // Check if it has live streaming details
            const hasLiveDetails =
              !!video.liveStreamingDetails?.actualStartTime;

            // Check if it's currently active (has actualStartTime but no actualEndTime)
            const isActive =
              !!video.liveStreamingDetails?.actualStartTime &&
              !video.liveStreamingDetails?.actualEndTime;

            // Check if it has a concurrent viewers count (crucial for truly live streams)
            const hasConcurrentViewers =
              !!video.liveStreamingDetails?.concurrentViewers &&
              parseInt(
                video.liveStreamingDetails?.concurrentViewers as string,
                10
              ) > 0;

            // Check if the video is embeddable (important for our player)
            const isEmbeddable = video.status?.embeddable !== false;

            // Check if the video was published very recently (within the last 30 minutes)
            const publishTime = new Date(
              video.snippet?.publishedAt || ""
            ).getTime();
            const isVeryRecent = Date.now() - publishTime < 30 * 60 * 1000;

            // Log detailed verification info
            console.log(`Verifying video "${video.snippet?.title}":`, {
              isMarkedLive,
              hasLiveDetails,
              isActive,
              hasConcurrentViewers,
              concurrentViewers:
                video.liveStreamingDetails?.concurrentViewers || 0,
              isEmbeddable,
              isVeryRecent,
              publishedAt: video.snippet?.publishedAt,
              actualStartTime:
                video.liveStreamingDetails?.actualStartTime || "none",
              actualEndTime:
                video.liveStreamingDetails?.actualEndTime || "none",
              scheduledStartTime:
                video.liveStreamingDetails?.scheduledStartTime || "none",
            });

            // Only return true if ALL essential conditions are met
            // We now require either concurrent viewers or a very recent publish time
            return (
              isMarkedLive &&
              hasLiveDetails &&
              isActive &&
              isEmbeddable &&
              (hasConcurrentViewers || isVeryRecent)
            );
          });

          console.log(
            `Verified ${trulyLiveVideos?.length || 0} of ${videoIds.length} videos are truly live`
          );

          // Replace the response items with only the truly live videos
          if (trulyLiveVideos && trulyLiveVideos.length > 0) {
            // Convert the video format to match our expected format
            response.data.items = trulyLiveVideos.map((video) => ({
              id: { videoId: video.id },
              snippet: video.snippet,
              // Add the actual start time for more accurate "Started X time ago" display
              actualStartTime: video.liveStreamingDetails?.actualStartTime,
            }));
          } else {
            // No truly live videos found
            response.data.items = [];
          }
        } catch (error) {
          console.error("Error verifying live videos:", error);
          // Continue with the original response if verification fails
        }
      }
    }

    // Log the response for debugging
    if (response.data.items && response.data.items.length > 0) {
      console.log(
        `Found ${response.data.items.length} live streams from direct API call`
      );
      response.data.items.forEach((item, index) => {
        console.log(
          `Live stream ${index + 1}: ${item.snippet?.title} (${item.snippet?.liveBroadcastContent})`
        );
      });
    } else {
      console.log("No live streams found from direct API call");
    }

    // If no live streams found, try to get recently completed live streams
    // that might still be processing (sometimes YouTube doesn't mark them as live immediately)
    if (!response.data.items || response.data.items.length === 0) {
      console.log(
        "No live streams found, checking for recent videos that might be live"
      );

      // First, try to get videos from the last hour (most likely to be live)
      console.log("Fetching very recent videos (last hour)");
      const veryRecentVideos = await youtube.search.list({
        key: apiKey,
        part: ["snippet"],
        channelId,
        type: ["video"],
        maxResults: 10, // Increased to get more potential matches
        order: "date", // Get the most recent videos
        publishedAfter: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // Last 1 hour
      });

      // Log the response for debugging
      if (
        veryRecentVideos.data.items &&
        veryRecentVideos.data.items.length > 0
      ) {
        console.log(
          `Found ${veryRecentVideos.data.items.length} very recent videos (last hour)`
        );
        veryRecentVideos.data.items.forEach((item, index) => {
          const publishTime = new Date(
            item.snippet?.publishedAt || ""
          ).getTime();
          const minutesAgo = Math.round(
            (Date.now() - publishTime) / (60 * 1000)
          );
          console.log(
            `Recent video ${index + 1}: ${item.snippet?.title} (${minutesAgo} minutes ago, ${item.snippet?.liveBroadcastContent})`
          );
        });
      } else {
        console.log("No very recent videos found");
      }

      // If we found very recent videos, check them first
      if (
        veryRecentVideos.data.items &&
        veryRecentVideos.data.items.length > 0
      ) {
        console.log(
          `Found ${veryRecentVideos.data.items.length} videos from the last hour`
        );

        // For very recent videos, we'll be more aggressive with detection
        // Any video from the last hour could potentially be a live stream
        response.data.items = veryRecentVideos.data.items;

        // Mark the most recent video as live if it's within the last 30 minutes
        if (veryRecentVideos.data.items.length > 0) {
          const mostRecent = veryRecentVideos.data.items[0];
          const publishTime = new Date(
            mostRecent.snippet?.publishedAt || ""
          ).getTime();
          const thirtyMinutesAgo = Date.now() - 30 * 60 * 1000;

          if (publishTime > thirtyMinutesAgo) {
            console.log(
              "Found extremely recent video (< 30 min), treating as potentially live:",
              mostRecent.snippet?.title
            );

            // Force it to be treated as live
            if (mostRecent.snippet) {
              mostRecent.snippet.liveBroadcastContent = "live";
            }
          }
        }
      } else {
        // If no very recent videos, try the last 3 hours
        console.log("Fetching recent videos (last 3 hours)");
        const recentVideos = await youtube.search.list({
          key: apiKey,
          part: ["snippet"],
          channelId,
          type: ["video"],
          maxResults: 10, // Increased to get more potential matches
          order: "date", // Get the most recent videos
          publishedAfter: new Date(
            Date.now() - 3 * 60 * 60 * 1000
          ).toISOString(), // Last 3 hours
        });

        // Log the response for debugging
        if (recentVideos.data.items && recentVideos.data.items.length > 0) {
          console.log(
            `Found ${recentVideos.data.items.length} recent videos (last 3 hours)`
          );
          recentVideos.data.items.forEach((item, index) => {
            const publishTime = new Date(
              item.snippet?.publishedAt || ""
            ).getTime();
            const minutesAgo = Math.round(
              (Date.now() - publishTime) / (60 * 1000)
            );
            console.log(
              `Recent video ${index + 1}: ${item.snippet?.title} (${minutesAgo} minutes ago, ${item.snippet?.liveBroadcastContent})`
            );
          });
        } else {
          console.log("No recent videos found in the last 3 hours");
        }

        // If we found recent videos, add them to the response
        if (recentVideos.data.items && recentVideos.data.items.length > 0) {
          console.log(
            `Found ${recentVideos.data.items.length} videos from the last 3 hours`
          );

          // Filter for videos that might be live streams based on title
          const potentialLiveStreams = recentVideos.data.items.filter(
            (item) => {
              const title = item.snippet?.title?.toLowerCase() || "";
              const description =
                item.snippet?.description?.toLowerCase() || "";

              // Check for strong live indicators
              const hasStrongLiveKeywords =
                title.includes("live now") ||
                title.includes("streaming now") ||
                title.includes("live stream") ||
                description.includes("live now") ||
                description.includes("streaming now");

              if (hasStrongLiveKeywords) {
                console.log(
                  "Found strong live keywords in video:",
                  item.snippet?.title
                );
                return true;
              }

              // Check for regular live indicators
              return (
                title.includes("live") ||
                title.includes("stream") ||
                title.includes("service") ||
                title.includes("worship") ||
                title.includes("sunday") ||
                title.includes("now") ||
                description.includes("live stream") ||
                description.includes("join us live")
              );
            }
          );

          if (potentialLiveStreams.length > 0) {
            console.log(
              `Found ${potentialLiveStreams.length} potential live streams`
            );
            response.data.items = potentialLiveStreams;

            // Mark the most recent potential live stream as live
            if (
              potentialLiveStreams.length > 0 &&
              potentialLiveStreams[0].snippet
            ) {
              potentialLiveStreams[0].snippet.liveBroadcastContent = "live";
            }
          }
        }
      }
    }

    // Validate response
    if (!response.data || !response.data.items) {
      console.warn("Invalid response from YouTube API:", response);
      return [];
    }

    // Filter out videos that are too old to be considered live
    const filteredItems = response.data.items.filter((item) => {
      // Only apply this filter to videos that aren't explicitly marked as live by YouTube
      if (item.snippet?.liveBroadcastContent === "live") {
        console.log(
          "Video explicitly marked as live by YouTube:",
          item.snippet?.title
        );
        return true; // Keep videos explicitly marked as live
      }

      // If it's explicitly marked as "none" (not live), check if it's very recent
      if (item.snippet?.liveBroadcastContent === "none") {
        const publishTime = new Date(item.snippet?.publishedAt || "").getTime();
        const thirtyMinutesAgo = Date.now() - 30 * 60 * 1000;

        // If it was published in the last 30 minutes, it might be a stream that just ended
        if (publishTime > thirtyMinutesAgo) {
          console.log(
            "Recent video (< 30 min) that might be a stream that just ended:",
            item.snippet?.title
          );
          // Check title for live indicators
          const title = item.snippet?.title?.toLowerCase() || "";
          if (
            title.includes("live") ||
            title.includes("stream") ||
            title.includes("service")
          ) {
            console.log(
              "Recent video has live indicators in title, treating as recently ended stream"
            );
            return true;
          }
        }
      }

      // Check if the video is too old
      const publishTime = new Date(item.snippet?.publishedAt || "").getTime();
      const twentyFourHoursAgo = Date.now() - 24 * 60 * 60 * 1000; // Increased to 24 hours to include today's streams

      if (publishTime < twentyFourHoursAgo) {
        console.log(
          "Video is older than 24 hours and not explicitly marked as live, not considering it live:",
          item.snippet?.title
        );
        return false;
      }

      return true;
    });

    const liveStreams =
      filteredItems.map((item) => ({
        id: item.id?.videoId || "",
        title: item.snippet?.title || "",
        description: item.snippet?.description || "",
        thumbnail:
          item.snippet?.thumbnails?.maxres?.url ||
          item.snippet?.thumbnails?.high?.url ||
          "",
        // Use actualStartTime if available (from our verification step), otherwise use publishedAt
        publishedAt:
          (item as any).actualStartTime || item.snippet?.publishedAt || "",
        channelTitle: item.snippet?.channelTitle || "",
        liveBroadcastContent: item.snippet?.liveBroadcastContent as
          | "live"
          | "upcoming"
          | "none",
        wasLive: true,
        // Add a flag to indicate this is a verified live stream
        verifiedLive: !!(item as any).actualStartTime,
      })) || [];

    // Cache the result with a short TTL since live status can change frequently
    cache.set(cacheKey, liveStreams, LIVE_STREAM_CACHE_TTL);

    return liveStreams;
  } catch (error) {
    console.error("Error fetching live streams:", error);

    // Check if the error is due to quota exceeded
    if (error instanceof Error && error.message.includes("quota")) {
      console.log(
        "YouTube API quota exceeded, returning cached live streams if available"
      );
      // Try to use cached data even if it's expired
      const expiredCache = cache.getExpired<YouTubeVideo[]>(
        `live-streams-${channelId}`
      );
      if (expiredCache && expiredCache.length > 0) {
        console.log("Using expired cache for live streams");
        return expiredCache;
      }
      // If no cached data, return empty array
      return [];
    }

    // Re-throw the error if it's a configuration issue
    if (
      error instanceof Error &&
      (error.message.includes("API key") ||
        error.message.includes("Channel ID"))
    ) {
      throw error;
    }
    return [];
  }
}

/**
 * Invalidate the cache for a specific channel
 * @param channelId The YouTube channel ID
 * @param type The type of cache to invalidate ('all', 'live', or 'sermons')
 */
export function invalidateCache(
  channelId: string,
  type: "all" | "live" | "sermons" = "all"
): void {
  if (type === "all" || type === "live") {
    cache.delete(`live-streams-${channelId}`);
    console.log(`Invalidated live streams cache for channel ${channelId}`);
  }

  if (type === "all" || type === "sermons") {
    cache.delete(`recent-sermons-${channelId}`);
    console.log(`Invalidated sermons cache for channel ${channelId}`);
  }
}

export async function getRecentSermons(
  channelId: string
): Promise<YouTubeVideo[]> {
  try {
    // Validate inputs
    if (!channelId) {
      throw new Error("Channel ID is required");
    }

    if (!apiKey) {
      throw new Error("YouTube API key is not configured");
    }

    // Check cache first
    const cacheKey = `recent-sermons-${channelId}`;
    const cachedData = cache.get<YouTubeVideo[]>(cacheKey);

    if (cachedData) {
      console.log("Using cached sermons data");
      return cachedData;
    }

    console.log("Fetching fresh sermons data from YouTube API");

    // Get videos in a single API call to reduce quota usage
    // We'll just get the most recent videos without distinguishing between live and regular
    const videos = await youtube.search.list({
      key: apiKey,
      part: ["snippet"],
      channelId,
      type: ["video"],
      maxResults: 50, // Increased to get more past sermons
      order: "date",
    });

    // Create empty placeholder for completedLiveStreams to maintain compatibility
    const completedLiveStreams = {
      data: { items: [] },
    };
    // Use the same data for regularVideos to maintain compatibility
    const regularVideos = videos;

    // Validate responses
    if (!completedLiveStreams.data || !regularVideos.data) {
      console.warn("Invalid response from YouTube API");
      return [];
    }

    // Combine both results
    const allVideos = [
      ...(completedLiveStreams.data.items || []),
      ...(regularVideos.data.items || []),
    ];

    // If no videos found, return empty array
    if (allVideos.length === 0) {
      console.log("No videos found for channel", channelId);
      return [];
    }

    // Remove duplicates by ID first
    const uniqueByIdVideos = Array.from(
      new Map(allVideos.map((item) => [item.id?.videoId, item])).values()
    );

    // Now check for duplicate titles and keep only the most recent one
    const titleMap = new Map();
    uniqueByIdVideos.forEach((item) => {
      const title = item.snippet?.title;
      if (title) {
        // If we haven't seen this title before, or this item is newer than the one we've seen
        const existingItem = titleMap.get(title);
        if (
          !existingItem ||
          new Date(item.snippet?.publishedAt || "") >
            new Date(existingItem.snippet?.publishedAt || "")
        ) {
          titleMap.set(title, item);
        }
      }
    });

    // Get the final list of unique videos (by both ID and title)
    const uniqueVideos = Array.from(titleMap.values());

    // Sort by publishedAt date
    uniqueVideos.sort((a, b) => {
      const dateA = new Date(a.snippet?.publishedAt || "");
      const dateB = new Date(b.snippet?.publishedAt || "");
      return dateB.getTime() - dateA.getTime();
    });

    const sermons = uniqueVideos.map((item) => ({
      id: item.id?.videoId || "",
      title: item.snippet?.title || "",
      description: item.snippet?.description || "",
      thumbnail:
        item.snippet?.thumbnails?.maxres?.url ||
        item.snippet?.thumbnails?.high?.url ||
        "",
      publishedAt: item.snippet?.publishedAt || "",
      channelTitle: item.snippet?.channelTitle || "",
      liveBroadcastContent: item.snippet?.liveBroadcastContent as
        | "live"
        | "upcoming"
        | "none",
      // Enhanced wasLive detection to catch more past live streams
      wasLive:
        item.snippet?.title?.toLowerCase().includes("live") ||
        item.snippet?.title?.toLowerCase().includes("stream") ||
        item.snippet?.title?.toLowerCase().includes("service") ||
        item.snippet?.title?.toLowerCase().includes("worship") ||
        item.snippet?.title?.toLowerCase().includes("sunday") ||
        item.snippet?.description?.toLowerCase().includes("live") ||
        item.snippet?.description?.toLowerCase().includes("stream") ||
        item.snippet?.description?.toLowerCase().includes("service") ||
        item.snippet?.description?.toLowerCase().includes("worship") ||
        false,
    }));

    // Cache the result with a longer TTL since sermons don't change as frequently
    cache.set(cacheKey, sermons, SERMONS_CACHE_TTL);

    return sermons;
  } catch (error) {
    console.error("Error fetching recent sermons:", error);

    // Check if the error is due to quota exceeded
    if (error instanceof Error && error.message.includes("quota")) {
      console.log(
        "YouTube API quota exceeded, returning cached sermons if available"
      );
      // Try to use cached data even if it's expired
      const expiredCache = cache.getExpired<YouTubeVideo[]>(
        `recent-sermons-${channelId}`
      );
      if (expiredCache && expiredCache.length > 0) {
        console.log("Using expired cache for sermons");
        return expiredCache;
      }
      // If no cached data, return empty array
      return [];
    }

    // Re-throw the error if it's a configuration issue
    if (
      error instanceof Error &&
      (error.message.includes("API key") ||
        error.message.includes("Channel ID"))
    ) {
      throw error;
    }
    return [];
  }
}
