"use client";

import { useEffect, useState } from "react";
import { ChevronLeft, ChevronRight, Quote } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { getClient } from "@/sanity/client";
import { getTestimonialsQuery } from "@/sanity/queries/testimonialQueries";
import { urlForImage } from "@/sanity/image";

interface Testimonial {
  quote: string;
  author: string;
  role?: string;
  image?: unknown;
}

export function TestimonialsSection() {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchTestimonials() {
      try {
        const data = await getClient().fetch(getTestimonialsQuery);
        setTestimonials(data);
      } catch {
        setError("Failed to load testimonials");
      } finally {
        setLoading(false);
      }
    }
    fetchTestimonials();
  }, []);

  const next = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prev = () => {
    setCurrentIndex(
      (prev) => (prev - 1 + testimonials.length) % testimonials.length
    );
  };

  if (loading) {
    return <div className="text-center py-20">Loading testimonials...</div>;
  }
  if (error) {
    return <div className="text-center py-20 text-red-500">{error}</div>;
  }
  if (!testimonials.length) {
    return (
      <div className="text-center py-20 text-muted-foreground">
        No testimonials found.
      </div>
    );
  }

  const testimonial = testimonials[currentIndex];

  return (
    <section className="py-20 bg-white dark:bg-gray-800">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold mb-4">Testimonials</h2>
          <p className="text-lg text-muted-foreground">
            Hear from our community
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="relative bg-secondary/30 rounded-2xl p-8 md:p-12">
            <Quote className="absolute text-primary/20 h-24 w-24 -top-6 -left-6" />

            <div className="relative z-10">
              <blockquote className="text-xl md:text-2xl mb-8 italic">
                &quot;{testimonial.quote}&quot;
              </blockquote>

              <div className="flex items-center gap-4">
                <div className="relative w-16 h-16">
                  {testimonial.image ? (
                    <Image
                      src={
                        urlForImage(testimonial.image as object)
                          .width(64)
                          .height(64)
                          .url() as string
                      }
                      alt={testimonial.author}
                      fill
                      className="rounded-full object-cover"
                      sizes="64px"
                    />
                  ) : (
                    <div className="w-16 h-16 rounded-full bg-gray-300" />
                  )}
                </div>
                <div>
                  <div className="font-semibold">{testimonial.author}</div>
                  <div className="text-muted-foreground">
                    {testimonial.role}
                  </div>
                </div>
              </div>
            </div>

            <div className="absolute bottom-4 right-4 flex gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={prev}
                className="rounded-full"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={next}
                className="rounded-full"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
