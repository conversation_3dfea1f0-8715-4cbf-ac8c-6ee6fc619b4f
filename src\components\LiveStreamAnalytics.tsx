/**
 * Live Stream Analytics Dashboard Component
 * Displays real-time metrics, viewer trends, and stream health
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  Clock, 
  Activity,
  Eye,
  BarChart3,
  Zap
} from 'lucide-react';
import { useRealtimeLiveStream } from '@/hooks/useRealtimeLiveStream';
import { StreamHealthMetrics } from '@/lib/youtube-realtime';

interface LiveStreamAnalyticsProps {
  className?: string;
  compact?: boolean;
}

export function LiveStreamAnalytics({ className = '', compact = false }: LiveStreamAnalyticsProps) {
  const realtimeStream = useRealtimeLiveStream({ autoConnect: true });

  if (!realtimeStream.isLive || !realtimeStream.liveStream) {
    return null;
  }

  const formatDuration = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m ${seconds % 60}s`;
  };

  const getHealthColor = (score: number): string => {
    if (score >= 80) return 'text-green-500';
    if (score >= 60) return 'text-blue-500';
    if (score >= 40) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getHealthBadgeVariant = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    if (score >= 40) return 'outline';
    return 'destructive';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'decreasing':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      case 'stable':
        return <Minus className="h-4 w-4 text-blue-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  if (compact) {
    return (
      <div className={`flex items-center gap-4 p-4 bg-background/50 rounded-lg border ${className}`}>
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{realtimeStream.viewerCount.toLocaleString()}</span>
          {realtimeStream.healthMetrics && getTrendIcon(realtimeStream.healthMetrics.viewerTrend)}
        </div>
        
        {realtimeStream.healthMetrics && (
          <>
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Peak: {realtimeStream.healthMetrics.peakViewers.toLocaleString()}</span>
            </div>
            
            <Badge 
              variant={getHealthBadgeVariant(realtimeStream.healthMetrics.healthScore)}
              className="text-xs"
            >
              {realtimeStream.healthMetrics.healthScore}% Health
            </Badge>
          </>
        )}
        
        {realtimeStream.liveStream.estimatedDuration && (
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{formatDuration(realtimeStream.liveStream.estimatedDuration)}</span>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {/* Current Viewers */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Current Viewers</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{realtimeStream.viewerCount.toLocaleString()}</div>
          {realtimeStream.healthMetrics && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              {getTrendIcon(realtimeStream.healthMetrics.viewerTrend)}
              <span className="capitalize">{realtimeStream.healthMetrics.viewerTrend}</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Peak Viewers */}
      {realtimeStream.healthMetrics && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Peak Viewers</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{realtimeStream.healthMetrics.peakViewers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Avg: {realtimeStream.healthMetrics.averageViewers.toLocaleString()}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Stream Duration */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Stream Duration</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {realtimeStream.liveStream.estimatedDuration 
              ? formatDuration(realtimeStream.liveStream.estimatedDuration)
              : 'Unknown'
            }
          </div>
          <p className="text-xs text-muted-foreground">
            Started {realtimeStream.liveStream.streamStartTime 
              ? new Date(realtimeStream.liveStream.streamStartTime).toLocaleTimeString()
              : 'Unknown'
            }
          </p>
        </CardContent>
      </Card>

      {/* Stream Health */}
      {realtimeStream.healthMetrics && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stream Health</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 mb-2">
              <div className={`text-2xl font-bold ${getHealthColor(realtimeStream.healthMetrics.healthScore)}`}>
                {realtimeStream.healthMetrics.healthScore}%
              </div>
              <Badge variant={getHealthBadgeVariant(realtimeStream.healthMetrics.healthScore)}>
                {realtimeStream.healthMetrics.healthScore >= 80 ? 'Excellent' :
                 realtimeStream.healthMetrics.healthScore >= 60 ? 'Good' :
                 realtimeStream.healthMetrics.healthScore >= 40 ? 'Fair' : 'Poor'}
              </Badge>
            </div>
            <Progress 
              value={realtimeStream.healthMetrics.healthScore} 
              className="h-2"
            />
          </CardContent>
        </Card>
      )}

      {/* Analytics Summary */}
      {realtimeStream.analytics && (
        <Card className="md:col-span-2 lg:col-span-4">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Live Analytics Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-muted-foreground">Detection Method</div>
                <div className="font-medium capitalize">{realtimeStream.analytics.verificationMethod}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Detection Latency</div>
                <div className="font-medium">{realtimeStream.analytics.detectionLatency}ms</div>
              </div>
              <div>
                <div className="text-muted-foreground">API Calls Used</div>
                <div className="font-medium">{realtimeStream.analytics.apiCallsUsed}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Last Updated</div>
                <div className="font-medium">
                  {realtimeStream.lastUpdate 
                    ? new Date(realtimeStream.lastUpdate).toLocaleTimeString()
                    : 'Never'
                  }
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default LiveStreamAnalytics;
