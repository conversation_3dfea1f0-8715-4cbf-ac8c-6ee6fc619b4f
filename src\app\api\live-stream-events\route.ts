/**
 * Server-Sent Events (SSE) endpoint for real-time live stream updates
 * Provides instant notifications when streams start/end and viewer counts change
 */

import { NextRequest } from 'next/server';
import { getRealtimeMonitor, LiveStreamEvent } from '@/lib/youtube-realtime';

// Keep track of active connections
const activeConnections = new Set<ReadableStreamDefaultController>();

export async function GET(request: NextRequest) {
  const channelId = process.env.YOUTUBE_CHANNEL_ID;
  
  if (!channelId) {
    return new Response('Channel ID not configured', { status: 500 });
  }

  // Create SSE headers
  const headers = new Headers({
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
  });

  // Create readable stream for SSE
  const stream = new ReadableStream({
    start(controller) {
      console.log('🔗 New SSE connection established');
      activeConnections.add(controller);

      // Get the real-time monitor
      const monitor = getRealtimeMonitor(channelId);

      // Send initial connection message
      const initialMessage = {
        type: 'connection_established',
        timestamp: new Date().toISOString(),
        data: {
          channelId,
          isMonitoring: monitor.isActive(),
          currentStream: monitor.getCurrentStream(),
          healthMetrics: monitor.getHealthMetrics()
        }
      };

      controller.enqueue(`data: ${JSON.stringify(initialMessage)}\n\n`);

      // Set up event listeners
      const handleStreamEvent = (event: LiveStreamEvent) => {
        try {
          controller.enqueue(`data: ${JSON.stringify(event)}\n\n`);
        } catch (error) {
          console.error('Error sending SSE event:', error);
        }
      };

      const handleMonitoringError = (error: any) => {
        const errorEvent = {
          type: 'error',
          timestamp: new Date().toISOString(),
          data: {
            message: error.error?.message || 'Unknown monitoring error',
            recoverable: true
          }
        };
        
        try {
          controller.enqueue(`data: ${JSON.stringify(errorEvent)}\n\n`);
        } catch (err) {
          console.error('Error sending error event:', err);
        }
      };

      // Register event listeners
      monitor.on('stream_started', handleStreamEvent);
      monitor.on('stream_ended', handleStreamEvent);
      monitor.on('viewer_count_updated', handleStreamEvent);
      monitor.on('stream_health_changed', handleStreamEvent);
      monitor.on('monitoring_error', handleMonitoringError);

      // Start monitoring if not already active
      if (!monitor.isActive()) {
        monitor.startMonitoring().catch(error => {
          console.error('Failed to start monitoring:', error);
          handleMonitoringError({ error });
        });
      }

      // Send periodic heartbeat
      const heartbeatInterval = setInterval(() => {
        try {
          const heartbeat = {
            type: 'heartbeat',
            timestamp: new Date().toISOString(),
            data: {
              connections: activeConnections.size,
              uptime: process.uptime(),
              memory: process.memoryUsage()
            }
          };
          controller.enqueue(`data: ${JSON.stringify(heartbeat)}\n\n`);
        } catch (error) {
          console.error('Heartbeat error:', error);
          clearInterval(heartbeatInterval);
        }
      }, 30000); // Every 30 seconds

      // Cleanup function
      const cleanup = () => {
        console.log('🔌 SSE connection closed');
        activeConnections.delete(controller);
        clearInterval(heartbeatInterval);
        
        // Remove event listeners
        monitor.off('stream_started', handleStreamEvent);
        monitor.off('stream_ended', handleStreamEvent);
        monitor.off('viewer_count_updated', handleStreamEvent);
        monitor.off('stream_health_changed', handleStreamEvent);
        monitor.off('monitoring_error', handleMonitoringError);

        // Stop monitoring if no more connections
        if (activeConnections.size === 0) {
          console.log('🛑 No more SSE connections, stopping monitoring');
          monitor.stopMonitoring();
        }
      };

      // Handle connection close
      request.signal.addEventListener('abort', cleanup);
      
      // Store cleanup function for manual cleanup if needed
      (controller as any).cleanup = cleanup;
    },

    cancel() {
      console.log('🚫 SSE stream cancelled');
      // Cleanup will be handled by the abort event
    }
  });

  return new Response(stream, { headers });
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Cache-Control',
    },
  });
}

// Utility function to broadcast message to all connections
export function broadcastToAllConnections(event: LiveStreamEvent) {
  const message = `data: ${JSON.stringify(event)}\n\n`;
  
  for (const controller of activeConnections) {
    try {
      controller.enqueue(message);
    } catch (error) {
      console.error('Error broadcasting to connection:', error);
      activeConnections.delete(controller);
    }
  }
}

// Health check endpoint
export async function POST(request: NextRequest) {
  const channelId = process.env.YOUTUBE_CHANNEL_ID;
  
  if (!channelId) {
    return Response.json({ error: 'Channel ID not configured' }, { status: 500 });
  }

  try {
    const monitor = getRealtimeMonitor(channelId);
    
    const status = {
      isMonitoring: monitor.isActive(),
      activeConnections: activeConnections.size,
      currentStream: monitor.getCurrentStream(),
      healthMetrics: monitor.getHealthMetrics(),
      viewerHistory: monitor.getViewerHistory().slice(-10), // Last 10 readings
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString()
    };

    return Response.json(status);
  } catch (error) {
    console.error('Health check error:', error);
    return Response.json(
      { 
        error: 'Health check failed', 
        details: error instanceof Error ? error.message : String(error) 
      }, 
      { status: 500 }
    );
  }
}
