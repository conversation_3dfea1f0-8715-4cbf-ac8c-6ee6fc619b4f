/**
 * Enhanced YouTube API module with advanced live stream detection and monitoring
 * Features: Multi-layer verification, intelligent caching, real-time updates, analytics
 */

import { google } from "googleapis";

// Initialize YouTube API client
const youtube = google.youtube("v3");
const apiKey = process.env.YOUTUBE_API_KEY;

// Define enhanced types
export interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  publishedAt: string;
  channelTitle: string;
  isLive: boolean;
  isPastLive: boolean;
  concurrentViewers?: number;
  verificationLevel?: "direct" | "api" | "rss" | "webhook";
  lastVerified?: string;
  streamQuality?: string;
  streamStartTime?: string;
  estimatedDuration?: number;
}

export interface LiveStreamAnalytics {
  peakViewers: number;
  averageViewers: number;
  streamDuration: number;
  detectionLatency: number;
  verificationMethod: string;
  apiCallsUsed: number;
}

export interface CacheEntry {
  data: {
    isLive: boolean;
    liveStream: YouTubeVideo | null;
    recentVideos: YouTubeVideo[];
    analytics?: LiveStreamAnalytics;
  };
  timestamp: number;
  ttl: number;
  hits: number;
  source: "api" | "direct" | "rss" | "webhook";
}

// Enhanced caching system with multiple TTL levels
let liveStatusCache: CacheEntry | null = null;
let emergencyCache: CacheEntry | null = null; // Longer-term fallback cache

// Dynamic cache TTL based on live status
const CACHE_TTL_LIVE = 30 * 1000; // 30 seconds when live (more frequent updates)
const CACHE_TTL_NOT_LIVE = 2 * 60 * 1000; // 2 minutes when not live
const EMERGENCY_CACHE_TTL = 30 * 60 * 1000; // 30 minutes for emergency fallback

// API quota management
let apiCallsToday = 0;
let lastResetDate = new Date().toDateString();
const MAX_API_CALLS_PER_DAY = 8000; // Conservative limit

// Circuit breaker for API failures
let circuitBreakerState: "closed" | "open" | "half-open" = "closed";
let failureCount = 0;
let lastFailureTime = 0;
const FAILURE_THRESHOLD = 5;
const CIRCUIT_BREAKER_TIMEOUT = 5 * 60 * 1000; // 5 minutes

// Utility functions for enhanced functionality
function resetApiQuotaIfNeeded() {
  const today = new Date().toDateString();
  if (lastResetDate !== today) {
    apiCallsToday = 0;
    lastResetDate = today;
    console.log("API quota reset for new day");
  }
}

function canMakeApiCall(): boolean {
  resetApiQuotaIfNeeded();
  return apiCallsToday < MAX_API_CALLS_PER_DAY;
}

function incrementApiCall() {
  resetApiQuotaIfNeeded();
  apiCallsToday++;
  console.log(`API calls today: ${apiCallsToday}/${MAX_API_CALLS_PER_DAY}`);
}

function updateCircuitBreaker(success: boolean) {
  if (success) {
    failureCount = 0;
    if (circuitBreakerState === "half-open") {
      circuitBreakerState = "closed";
      console.log("Circuit breaker closed - API calls restored");
    }
  } else {
    failureCount++;
    lastFailureTime = Date.now();

    if (failureCount >= FAILURE_THRESHOLD && circuitBreakerState === "closed") {
      circuitBreakerState = "open";
      console.log("Circuit breaker opened - API calls suspended");
    }
  }
}

function canUseApi(): boolean {
  if (circuitBreakerState === "closed") return true;
  if (circuitBreakerState === "open") {
    if (Date.now() - lastFailureTime > CIRCUIT_BREAKER_TIMEOUT) {
      circuitBreakerState = "half-open";
      console.log("Circuit breaker half-open - testing API");
      return true;
    }
    return false;
  }
  return true; // half-open state
}

function getDynamicCacheTTL(isLive: boolean): number {
  return isLive ? CACHE_TTL_LIVE : CACHE_TTL_NOT_LIVE;
}

function isCacheValid(
  cache: CacheEntry | null,
  forceRefresh: boolean
): boolean {
  if (!cache || forceRefresh) return false;
  const age = Date.now() - cache.timestamp;
  return age < cache.ttl;
}

function updateCache(
  data: CacheEntry["data"],
  source: CacheEntry["source"],
  isLive: boolean
) {
  const ttl = getDynamicCacheTTL(isLive);

  // Update main cache
  if (liveStatusCache) {
    liveStatusCache.hits++;
  }

  liveStatusCache = {
    data,
    timestamp: Date.now(),
    ttl,
    hits: liveStatusCache?.hits || 0,
    source,
  };

  // Update emergency cache with longer TTL
  emergencyCache = {
    data,
    timestamp: Date.now(),
    ttl: EMERGENCY_CACHE_TTL,
    hits: 0,
    source,
  };

  console.log(`Cache updated (${source}): TTL=${ttl}ms, Live=${isLive}`);
}

export async function getLiveStreamStatus(
  channelId: string,
  forceRefresh = false
): Promise<{
  isLive: boolean;
  liveStream: YouTubeVideo | null;
  recentVideos: YouTubeVideo[];
  cached: boolean;
  analytics?: LiveStreamAnalytics;
  cacheInfo?: {
    source: string;
    age: number;
    hits: number;
    ttl: number;
  };
}> {
  const startTime = Date.now();

  try {
    if (!apiKey) {
      throw new Error("YouTube API key is not configured");
    }

    if (!channelId) {
      throw new Error("Channel ID is required");
    }

    // Check cache validity with enhanced logic
    if (isCacheValid(liveStatusCache, forceRefresh)) {
      console.log(
        `Using cached live status data (age: ${Date.now() - liveStatusCache!.timestamp}ms)`
      );
      liveStatusCache!.hits++;

      return {
        ...liveStatusCache!.data,
        cached: true,
        cacheInfo: {
          source: liveStatusCache!.source,
          age: Date.now() - liveStatusCache!.timestamp,
          hits: liveStatusCache!.hits,
          ttl: liveStatusCache!.ttl,
        },
      };
    }

    console.log(
      `Checking live status for channel ${channelId} (Enhanced Detection)`
    );

    // Check API availability before making calls
    if (!canUseApi() || !canMakeApiCall()) {
      console.log("API unavailable, using emergency cache or fallback");
      if (emergencyCache) {
        console.log("Using emergency cache");
        return {
          ...emergencyCache.data,
          cached: true,
          cacheInfo: {
            source: `emergency-${emergencyCache.source}`,
            age: Date.now() - emergencyCache.timestamp,
            hits: emergencyCache.hits,
            ttl: emergencyCache.ttl,
          },
        };
      }
      // If no emergency cache, continue with limited functionality
    }

    // STEP 1: Enhanced live stream detection with multiple verification layers
    let potentialLiveStreams: any[] = [];
    let apiCallsUsed = 0;
    let detectionMethod = "none";

    try {
      if (canUseApi() && canMakeApiCall()) {
        console.log("STEP 1: YouTube API live search");
        console.log(
          `🔍 Searching for live streams ONLY from channel: ${channelId}`
        );
        console.log(
          `Expected channel: Times and Seasons Church (Revival Fire Missions Int'l)`
        );

        incrementApiCall();
        apiCallsUsed++;

        const liveSearchResponse = await youtube.search.list({
          key: apiKey,
          part: ["snippet", "id"],
          channelId, // This should filter to only our channel
          eventType: "live",
          type: ["video"],
          maxResults: 3,
        });

        potentialLiveStreams = liveSearchResponse.data.items || [];
        detectionMethod = "api-search";
        updateCircuitBreaker(true);

        console.log(
          `🔍 Found ${potentialLiveStreams.length} potential live streams from enhanced API search`
        );

        // Log each potential live stream for debugging
        potentialLiveStreams.forEach((item, index) => {
          console.log(`📺 Potential Live Stream ${index + 1}:`);
          console.log(`   Title: ${item.snippet?.title}`);
          console.log(`   Channel: ${item.snippet?.channelTitle}`);
          console.log(`   Channel ID: ${item.snippet?.channelId}`);
          console.log(`   Video ID: ${item.id?.videoId}`);
          console.log(`   Expected Channel ID: ${channelId}`);
          console.log(
            `   Channel Match: ${item.snippet?.channelId === channelId ? "✅ YES" : "❌ NO - THIS IS THE PROBLEM!"}`
          );
          console.log("");
        });
      } else {
        console.log("API unavailable, skipping YouTube API search");
      }
    } catch (apiError) {
      console.error("YouTube API search failed:", apiError);
      updateCircuitBreaker(false);
      // Continue with other detection methods
    }

    // STEP 2: Enhanced verification with detailed analytics
    let verifiedLiveStreams: any[] = [];

    if (potentialLiveStreams.length > 0) {
      try {
        if (canUseApi() && canMakeApiCall()) {
          console.log("STEP 2: Enhanced live stream verification");
          incrementApiCall();
          apiCallsUsed++;

          const videoIds = potentialLiveStreams
            .map((item) => item.id?.videoId)
            .filter(Boolean) as string[];

          const videoDetailsResponse = await youtube.videos.list({
            key: apiKey,
            part: ["snippet", "liveStreamingDetails", "statistics", "status"],
            id: videoIds,
          });

          updateCircuitBreaker(true);

          // Enhanced verification with multiple criteria
          verifiedLiveStreams =
            videoDetailsResponse.data.items?.filter((video) => {
              // CRITICAL: First check if this video belongs to our channel
              const videoChannelId = video.snippet?.channelId;
              const belongsToOurChannel = videoChannelId === channelId;

              if (!belongsToOurChannel) {
                console.log(
                  `❌ REJECTING video "${video.snippet?.title}" - belongs to different channel: ${videoChannelId} (expected: ${channelId})`
                );
                return false;
              }

              // Enhanced verification criteria
              const isMarkedLive =
                video.snippet?.liveBroadcastContent === "live";
              const hasStarted = !!video.liveStreamingDetails?.actualStartTime;
              const hasNotEnded = !video.liveStreamingDetails?.actualEndTime;
              const hasConcurrentViewers =
                !!video.liveStreamingDetails?.concurrentViewers;
              const isPublic = video.status?.privacyStatus === "public";

              // More sophisticated timing checks
              const publishTime = new Date(
                video.snippet?.publishedAt || ""
              ).getTime();
              const startTime = new Date(
                video.liveStreamingDetails?.actualStartTime || ""
              ).getTime();
              const isVeryRecent = Date.now() - publishTime < 15 * 60 * 1000; // 15 minutes
              const isActiveStream =
                startTime > 0 && Date.now() - startTime < 12 * 60 * 60 * 1000; // 12 hours max

              // Enhanced live detection logic
              const isTrulyLive =
                isMarkedLive &&
                hasStarted &&
                hasNotEnded &&
                isPublic &&
                (hasConcurrentViewers || isVeryRecent) &&
                isActiveStream;

              const viewerCount =
                parseInt(
                  video.liveStreamingDetails?.concurrentViewers as string,
                  10
                ) || 0;

              // Enhanced logging for debugging
              console.log(
                `✅ Enhanced verification for "${video.snippet?.title}" (Channel: ${videoChannelId}):`,
                {
                  belongsToOurChannel,
                  isMarkedLive,
                  hasStarted,
                  hasNotEnded,
                  isPublic,
                  hasConcurrentViewers,
                  viewerCount,
                  isVeryRecent,
                  isActiveStream,
                  publishedAt: video.snippet?.publishedAt,
                  actualStartTime: video.liveStreamingDetails?.actualStartTime,
                  minutesAgo: Math.round(
                    (Date.now() - publishTime) / (60 * 1000)
                  ),
                  isTrulyLive,
                }
              );

              return isTrulyLive;
            }) || [];
        } else {
          console.log(
            "API unavailable for verification, skipping detailed verification"
          );
          verifiedLiveStreams = [];
        }
      } catch (verificationError) {
        console.error("Live stream verification failed:", verificationError);
        updateCircuitBreaker(false);
        verifiedLiveStreams = [];
      }
    } else {
      console.log("No potential live streams to verify");
    }

    console.log(`Found ${verifiedLiveStreams.length} verified live streams`);

    // STEP 3: Process verified live streams with enhanced data
    if (verifiedLiveStreams.length > 0) {
      const liveVideo = verifiedLiveStreams[0];
      const detectionLatency = Date.now() - startTime;
      const viewerCount =
        parseInt(
          liveVideo.liveStreamingDetails?.concurrentViewers as string,
          10
        ) || 0;

      // Enhanced live stream object with additional metadata
      const liveStream: YouTubeVideo = {
        id: liveVideo.id || "",
        title: liveVideo.snippet?.title || "",
        description: liveVideo.snippet?.description || "",
        thumbnail: `https://i.ytimg.com/vi/${liveVideo.id}/hqdefault.jpg`,
        publishedAt: liveVideo.snippet?.publishedAt || "",
        channelTitle: liveVideo.snippet?.channelTitle || "",
        isLive: true,
        isPastLive: false,
        concurrentViewers: viewerCount,
        verificationLevel: "api",
        lastVerified: new Date().toISOString(),
        streamStartTime: liveVideo.liveStreamingDetails?.actualStartTime,
        estimatedDuration: liveVideo.liveStreamingDetails?.actualStartTime
          ? Date.now() -
            new Date(liveVideo.liveStreamingDetails.actualStartTime).getTime()
          : undefined,
      };

      // Create analytics data
      const analytics: LiveStreamAnalytics = {
        peakViewers: viewerCount, // Will be updated over time
        averageViewers: viewerCount,
        streamDuration: liveStream.estimatedDuration || 0,
        detectionLatency,
        verificationMethod: detectionMethod,
        apiCallsUsed,
      };

      console.log(
        `🔴 LIVE DETECTED: "${liveStream.title}" | ${liveStream.concurrentViewers} viewers | Detection: ${detectionLatency}ms | Method: ${detectionMethod}`
      );

      // Get recent videos as well
      const recentVideos = await getRecentVideos(channelId, liveStream.id);

      // Enhanced result with analytics
      const result = {
        isLive: true,
        liveStream,
        recentVideos,
        analytics,
      };

      // Update cache with enhanced data
      updateCache(result, "api", true);

      return {
        ...result,
        cached: false,
        cacheInfo: {
          source: "fresh-api",
          age: 0,
          hits: 0,
          ttl: getDynamicCacheTTL(true),
        },
      };
    }

    // STEP 4: No live stream found - enhanced fallback
    const detectionLatency = Date.now() - startTime;
    console.log(
      `❌ No live streams found | Detection time: ${detectionLatency}ms | Method: ${detectionMethod} | API calls: ${apiCallsUsed}`
    );

    // Get recent videos with enhanced error handling
    let recentVideos: YouTubeVideo[] = [];
    try {
      if (canUseApi() && canMakeApiCall()) {
        recentVideos = await getRecentVideos(channelId);
        incrementApiCall();
        apiCallsUsed++;
      } else {
        console.log("API unavailable for recent videos, using empty array");
      }
    } catch (recentVideosError) {
      console.error("Failed to fetch recent videos:", recentVideosError);
      updateCircuitBreaker(false);
    }

    // Create analytics for non-live state
    const analytics: LiveStreamAnalytics = {
      peakViewers: 0,
      averageViewers: 0,
      streamDuration: 0,
      detectionLatency,
      verificationMethod: detectionMethod,
      apiCallsUsed,
    };

    // Enhanced result for non-live state
    const result = {
      isLive: false,
      liveStream: null,
      recentVideos,
      analytics,
    };

    // Update cache with enhanced data
    updateCache(result, "api", false);

    return {
      ...result,
      cached: false,
      cacheInfo: {
        source: "fresh-api",
        age: 0,
        hits: 0,
        ttl: getDynamicCacheTTL(false),
      },
    };
  } catch (error) {
    const detectionLatency = Date.now() - startTime;
    console.error(
      `❌ Critical error in live status detection (${detectionLatency}ms):`,
      error
    );

    updateCircuitBreaker(false);

    // Enhanced error handling with multiple fallback strategies
    const isQuotaError =
      error instanceof Error &&
      (error.message.includes("quota") || error.message.includes("429"));

    const isNetworkError =
      error instanceof Error &&
      (error.message.includes("ENOTFOUND") ||
        error.message.includes("timeout"));

    // Strategy 1: Use main cache if available
    if (liveStatusCache && isCacheValid(liveStatusCache, false)) {
      console.log("🔄 Using main cache due to error");
      liveStatusCache.hits++;
      return {
        ...liveStatusCache.data,
        cached: true,
        cacheInfo: {
          source: `error-fallback-${liveStatusCache.source}`,
          age: Date.now() - liveStatusCache.timestamp,
          hits: liveStatusCache.hits,
          ttl: liveStatusCache.ttl,
        },
      };
    }

    // Strategy 2: Use emergency cache even if expired
    if (emergencyCache) {
      console.log("🆘 Using emergency cache due to critical error");
      return {
        ...emergencyCache.data,
        cached: true,
        cacheInfo: {
          source: `emergency-${emergencyCache.source}`,
          age: Date.now() - emergencyCache.timestamp,
          hits: emergencyCache.hits,
          ttl: emergencyCache.ttl,
        },
      };
    }

    // Strategy 3: Return enhanced default response with error context
    const errorAnalytics: LiveStreamAnalytics = {
      peakViewers: 0,
      averageViewers: 0,
      streamDuration: 0,
      detectionLatency,
      verificationMethod: "error",
      apiCallsUsed: 0,
    };

    console.log("⚠️ Returning default response due to complete failure");
    return {
      isLive: false,
      liveStream: null,
      recentVideos: [],
      cached: false,
      analytics: errorAnalytics,
    };
  }
}

/**
 * Gets recent videos from the channel
 */
async function getRecentVideos(
  channelId: string,
  excludeVideoId?: string
): Promise<YouTubeVideo[]> {
  try {
    // Get recent videos
    const recentVideosResponse = await youtube.search.list({
      key: apiKey,
      part: ["snippet", "id"],
      channelId,
      type: ["video"],
      maxResults: 20,
      order: "date",
    });

    if (
      !recentVideosResponse.data.items ||
      recentVideosResponse.data.items.length === 0
    ) {
      return [];
    }

    // Get detailed information about these videos
    const videoIds = recentVideosResponse.data.items
      .map((item) => item.id?.videoId)
      .filter(Boolean) as string[];

    const videoDetailsResponse = await youtube.videos.list({
      key: apiKey,
      part: ["snippet", "liveStreamingDetails", "status"],
      id: videoIds,
    });

    if (!videoDetailsResponse.data.items) {
      return [];
    }

    // Process videos
    const processedVideos = videoDetailsResponse.data.items.map((video) => {
      // Check if this was a live stream that has ended
      // Primary indicator: it has both start and end times in liveStreamingDetails
      const hasLiveStreamingDetails =
        video.liveStreamingDetails?.actualStartTime !== undefined &&
        video.liveStreamingDetails?.actualEndTime !== undefined;

      // Secondary indicators: title/description contains service-related keywords
      const titleHasServiceKeywords =
        video.snippet?.title?.toLowerCase().includes("service") ||
        video.snippet?.title?.toLowerCase().includes("sunday") ||
        video.snippet?.title?.toLowerCase().includes("worship") ||
        video.snippet?.title?.toLowerCase().includes("sermon");

      // Check if it was published recently (within last 24 hours)
      const publishTime = new Date(video.snippet?.publishedAt || "").getTime();
      const isRecent = Date.now() - publishTime < 24 * 60 * 60 * 1000;

      // A video is a past live stream if:
      // 1. It has live streaming details with start and end times, OR
      // 2. It has service keywords in the title AND was published recently
      const isPastLive =
        hasLiveStreamingDetails || (titleHasServiceKeywords && isRecent);

      // Log detailed information for debugging past live streams
      if (isPastLive) {
        console.log(`Found past live stream: "${video.snippet?.title}"`, {
          hasLiveStreamingDetails,
          titleHasServiceKeywords,
          isRecent,
          publishedAt: video.snippet?.publishedAt,
          hoursAgo: Math.round((Date.now() - publishTime) / (60 * 60 * 1000)),
        });
      }

      return {
        id: video.id || "",
        title: video.snippet?.title || "",
        description: video.snippet?.description || "",
        thumbnail: `https://i.ytimg.com/vi/${video.id}/hqdefault.jpg`,
        publishedAt: video.snippet?.publishedAt || "",
        channelTitle: video.snippet?.channelTitle || "",
        isLive: false, // These are not live (we already checked for live streams)
        isPastLive,
      };
    });

    // Remove duplicates by title
    const uniqueVideos = new Map<string, YouTubeVideo>();
    processedVideos.forEach((video) => {
      // Skip the excluded video ID (current live stream)
      if (excludeVideoId && video.id === excludeVideoId) {
        return;
      }

      const existingVideo = uniqueVideos.get(video.title);
      if (
        !existingVideo ||
        new Date(video.publishedAt) > new Date(existingVideo.publishedAt)
      ) {
        uniqueVideos.set(video.title, video);
      }
    });

    // Convert to array and sort by date (newest first)
    return Array.from(uniqueVideos.values()).sort(
      (a, b) =>
        new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
    );
  } catch (error) {
    console.error("Error fetching recent videos:", error);
    return [];
  }
}
