"use client";
import { CalendarDays, Clock, MapPin } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { getClient } from "@/sanity/client";
import { getEventsQuery } from "@/sanity/queries/eventQueries";
import Link from "next/link";

interface Event {
  title: string;
  date: string;
  time: string;
  location: string;
  description?: string;
}

export function UpcomingEvents() {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchEvents() {
      try {
        const data = await getClient().fetch(getEventsQuery);
        setEvents(data);
      } catch {
        setError("Failed to load events");
      } finally {
        setLoading(false);
      }
    }
    fetchEvents();
  }, []);

  if (loading) {
    return <div className="text-center py-20">Loading events...</div>;
  }
  if (error) {
    return <div className="text-center py-20 text-red-500">{error}</div>;
  }

  return (
    <section className="py-20 bg-white dark:bg-gray-800">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold mb-4">Upcoming Events</h2>
          <p className="text-lg text-muted-foreground">
            Join us in fellowship and worship
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {events.map((event, index) => (
            <div
              key={index}
              className="bg-secondary/50 rounded-xl p-6 hover:shadow-lg transition-shadow"
            >
              <h3 className="text-2xl font-semibold mb-3">{event.title}</h3>
              <div className="space-y-3 mb-4">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <CalendarDays className="h-5 w-5" />
                  <span>{event.date}</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="h-5 w-5" />
                  <span>{event.time}</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <MapPin className="h-5 w-5" />
                  <span>{event.location}</span>
                </div>
              </div>
              <p className="mb-4 text-muted-foreground">{event.description}</p>
              <Button variant="outline" className="w-full">
                Learn More
              </Button>
            </div>
          ))}
        </div>
        <div className="mt-10 flex justify-center">
          <Link href="/churches">
            <Button size="lg" className="text-lg font-semibold">
              View All Church Branches
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
