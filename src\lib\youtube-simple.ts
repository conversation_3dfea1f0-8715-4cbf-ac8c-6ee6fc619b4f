/**
 * Simplified YouTube API module for fetching sermons
 * This module focuses on reliability and simplicity over complex detection logic
 */

import { google } from "googleapis";

// Initialize YouTube API client
const youtube = google.youtube("v3");
const apiKey = process.env.YOUTUBE_API_KEY;

// Define types
export interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  publishedAt: string;
  channelTitle: string;
  isLive: boolean;
  isPastLive: boolean;
}

/**
 * Fetches all videos from a YouTube channel
 * @param channelId The YouTube channel ID
 * @returns A promise that resolves to an array of videos
 */
export async function getAllVideos(channelId: string): Promise<{
  videos: YouTubeVideo[];
  currentLiveStream: YouTubeVideo | null;
  recentStream: YouTubeVideo | null;
}> {
  try {
    if (!apiKey) {
      throw new Error("YouTube API key is not configured");
    }

    if (!channelId) {
      throw new Error("Channel ID is required");
    }

    console.log(`Fetching videos for channel ${channelId}`);

    // Step 1: Get all recent videos from the channel (up to 50)
    const searchResponse = await youtube.search.list({
      key: apiKey,
      part: ["snippet", "id"],
      channelId,
      type: ["video"],
      maxResults: 50,
      order: "date",
    });

    if (!searchResponse.data.items || searchResponse.data.items.length === 0) {
      console.log("No videos found for channel");
      return { videos: [], currentLiveStream: null, recentStream: null };
    }

    console.log(`Found ${searchResponse.data.items.length} videos`);

    // Step 2: Get detailed information about these videos
    const videoIds = searchResponse.data.items
      .map((item) => item.id?.videoId)
      .filter(Boolean) as string[];

    const videoDetailsResponse = await youtube.videos.list({
      key: apiKey,
      part: ["snippet", "liveStreamingDetails", "status"],
      id: videoIds,
    });

    if (!videoDetailsResponse.data.items) {
      console.log("No video details found");
      return { videos: [], currentLiveStream: null, recentStream: null };
    }

    // Step 3: Process videos and detect live streams
    const processedVideos: YouTubeVideo[] = [];
    let currentLiveStream: YouTubeVideo | null = null;
    let recentStream: YouTubeVideo | null = null;

    // Log if we found any videos with concurrent viewers (truly live)
    const videosWithConcurrentViewers = videoDetailsResponse.data.items?.filter(
      (video) => !!video.liveStreamingDetails?.concurrentViewers
    );

    if (videosWithConcurrentViewers && videosWithConcurrentViewers.length > 0) {
      console.log(
        `Found ${videosWithConcurrentViewers.length} videos with concurrent viewers (truly live):`
      );
      videosWithConcurrentViewers.forEach((video) => {
        console.log(
          `- "${video.snippet?.title}": ${video.liveStreamingDetails?.concurrentViewers} viewers`
        );
      });
    } else {
      console.log(
        "No videos with concurrent viewers found - no truly live streams"
      );
    }

    // Map to store videos by title to handle duplicates
    const videosByTitle = new Map<string, YouTubeVideo>();

    videoDetailsResponse.data.items.forEach((video) => {
      if (!video.snippet) return;

      // Check if this is a live stream - be very strict about what counts as live
      const isLive =
        // Must be marked as live by YouTube
        video.snippet.liveBroadcastContent === "live" &&
        // Must have started (have an actual start time)
        video.liveStreamingDetails?.actualStartTime &&
        // Must not have ended
        !video.liveStreamingDetails?.actualEndTime &&
        // Must have concurrent viewers (crucial for truly live streams)
        !!video.liveStreamingDetails?.concurrentViewers;

      // Check if this was a live stream that has ended
      const isPastLive =
        // Most reliable indicator: it has both start and end times in liveStreamingDetails
        (video.liveStreamingDetails?.actualStartTime !== undefined &&
          video.liveStreamingDetails?.actualEndTime !== undefined) ||
        // Secondary indicators: title/description contains service-related keywords
        video.snippet.title.toLowerCase().includes("service") ||
        video.snippet.title.toLowerCase().includes("sunday") ||
        video.snippet.title.toLowerCase().includes("worship") ||
        video.snippet.title.toLowerCase().includes("sermon") ||
        video.snippet.description?.toLowerCase().includes("live stream") ||
        video.snippet.description?.toLowerCase().includes("service");

      const processedVideo: YouTubeVideo = {
        id: video.id || "",
        title: video.snippet.title || "",
        description: video.snippet.description || "",
        thumbnail:
          video.snippet.thumbnails?.maxres?.url ||
          video.snippet.thumbnails?.high?.url ||
          video.snippet.thumbnails?.default?.url ||
          "",
        publishedAt: video.snippet.publishedAt || "",
        channelTitle: video.snippet.channelTitle || "",
        isLive,
        isPastLive,
      };

      // Handle duplicates by keeping only the most recent version of each title
      const existingVideo = videosByTitle.get(processedVideo.title);
      if (existingVideo) {
        const existingDate = new Date(existingVideo.publishedAt);
        const currentDate = new Date(processedVideo.publishedAt);

        if (currentDate > existingDate) {
          videosByTitle.set(processedVideo.title, processedVideo);
        }
      } else {
        videosByTitle.set(processedVideo.title, processedVideo);
      }

      // Log detailed information about potential live streams
      if (video.snippet.liveBroadcastContent === "live") {
        console.log(`Potential live stream: "${video.snippet.title}"`, {
          isLive,
          liveBroadcastContent: video.snippet.liveBroadcastContent,
          hasActualStartTime: !!video.liveStreamingDetails?.actualStartTime,
          hasActualEndTime: !!video.liveStreamingDetails?.actualEndTime,
          hasConcurrentViewers: !!video.liveStreamingDetails?.concurrentViewers,
          concurrentViewers: video.liveStreamingDetails?.concurrentViewers || 0,
          publishedAt: video.snippet.publishedAt,
        });
      }

      // Track current live stream
      if (
        isLive &&
        (!currentLiveStream ||
          new Date(processedVideo.publishedAt) >
            new Date(currentLiveStream.publishedAt))
      ) {
        console.log(`Found VERIFIED live stream: "${processedVideo.title}"`);
        currentLiveStream = processedVideo;
      }
    });

    // Convert map to array and sort by date (newest first)
    const videos = Array.from(videosByTitle.values()).sort(
      (a, b) =>
        new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
    );

    // Find the most recent past live stream if no current live stream
    if (!currentLiveStream && videos.length > 0) {
      const pastLiveStreams = videos.filter((v) => v.isPastLive);
      if (pastLiveStreams.length > 0) {
        recentStream = pastLiveStreams[0]; // Already sorted by date
      }
    }

    return {
      videos,
      currentLiveStream,
      recentStream,
    };
  } catch (error) {
    console.error("Error fetching YouTube videos:", error);
    throw error;
  }
}
