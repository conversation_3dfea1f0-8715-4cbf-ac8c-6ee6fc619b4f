/**
 * Live Stream Archive System
 * Automatically saves ended live streams and makes them accessible as replays
 */

import { google } from "googleapis";
import { YouTubeVideo } from "@/types/youtube";

const youtube = google.youtube("v3");
const apiKey = process.env.YOUTUBE_API_KEY;

export interface ArchivedLiveStream extends YouTubeVideo {
  originalLiveStartTime: string;
  originalLiveEndTime: string;
  streamDuration: number; // in seconds
  peakViewers?: number;
  averageViewers?: number;
  archivedAt: string;
  replayAvailable: boolean;
  processingStatus: 'processing' | 'completed' | 'failed';
  streamAnalytics?: {
    totalViews: number;
    likes: number;
    comments: number;
    shares: number;
  };
}

export interface StreamArchiveResult {
  success: boolean;
  archivedStream?: ArchivedLiveStream;
  error?: string;
  processingTimeEstimate?: number; // in minutes
}

/**
 * Archives a live stream that has just ended
 * @param endedStream - The live stream that just ended
 * @param streamAnalytics - Optional analytics data from the live stream
 * @returns Promise<StreamArchiveResult>
 */
export async function archiveEndedLiveStream(
  endedStream: YouTubeVideo,
  streamAnalytics?: {
    peakViewers?: number;
    averageViewers?: number;
    streamDuration?: number;
  }
): Promise<StreamArchiveResult> {
  try {
    console.log(`📼 Starting archive process for ended live stream: "${endedStream.title}"`);
    
    if (!apiKey) {
      throw new Error("YouTube API key not configured");
    }

    // Step 1: Verify the stream has actually ended and get detailed info
    const videoDetails = await youtube.videos.list({
      key: apiKey,
      part: ["snippet", "liveStreamingDetails", "statistics", "status", "contentDetails"],
      id: [endedStream.id],
    });

    if (!videoDetails.data.items || videoDetails.data.items.length === 0) {
      throw new Error(`Video ${endedStream.id} not found`);
    }

    const video = videoDetails.data.items[0];
    const liveDetails = video.liveStreamingDetails;
    
    // Verify this was actually a live stream that has ended
    if (!liveDetails?.actualStartTime || !liveDetails?.actualEndTime) {
      throw new Error("Video is not a completed live stream");
    }

    // Step 2: Calculate stream duration
    const startTime = new Date(liveDetails.actualStartTime).getTime();
    const endTime = new Date(liveDetails.actualEndTime).getTime();
    const streamDuration = Math.floor((endTime - startTime) / 1000); // in seconds

    // Step 3: Get current statistics
    const statistics = video.statistics;
    const currentViews = parseInt(statistics?.viewCount || "0", 10);
    const likes = parseInt(statistics?.likeCount || "0", 10);
    const comments = parseInt(statistics?.commentCount || "0", 10);

    // Step 4: Determine processing status
    // YouTube typically takes 2-6 hours to fully process live stream recordings
    const processingStatus = determineProcessingStatus(video);
    const estimatedProcessingTime = estimateProcessingTime(streamDuration);

    // Step 5: Create archived stream object
    const archivedStream: ArchivedLiveStream = {
      id: endedStream.id,
      title: endedStream.title,
      description: endedStream.description,
      thumbnail: endedStream.thumbnail,
      publishedAt: endedStream.publishedAt,
      channelTitle: endedStream.channelTitle,
      isLive: false,
      isPastLive: true,
      originalLiveStartTime: liveDetails.actualStartTime,
      originalLiveEndTime: liveDetails.actualEndTime,
      streamDuration,
      peakViewers: streamAnalytics?.peakViewers || 0,
      averageViewers: streamAnalytics?.averageViewers || 0,
      archivedAt: new Date().toISOString(),
      replayAvailable: processingStatus === 'completed',
      processingStatus,
      streamAnalytics: {
        totalViews: currentViews,
        likes,
        comments,
        shares: 0, // YouTube API doesn't provide share count
      },
    };

    console.log(`✅ Successfully archived live stream: "${endedStream.title}"`);
    console.log(`📊 Stream duration: ${Math.floor(streamDuration / 60)} minutes`);
    console.log(`🎬 Processing status: ${processingStatus}`);
    console.log(`⏱️ Estimated processing time: ${estimatedProcessingTime} minutes`);

    return {
      success: true,
      archivedStream,
      processingTimeEstimate: estimatedProcessingTime,
    };

  } catch (error) {
    console.error("❌ Error archiving live stream:", error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Determines the processing status of a video
 */
function determineProcessingStatus(video: any): 'processing' | 'completed' | 'failed' {
  const uploadStatus = video.status?.uploadStatus;
  const privacyStatus = video.status?.privacyStatus;
  
  // Check if video is fully processed and available
  if (uploadStatus === 'processed' && privacyStatus === 'public') {
    return 'completed';
  }
  
  // Check for failed processing
  if (uploadStatus === 'failed' || uploadStatus === 'rejected') {
    return 'failed';
  }
  
  // Default to processing
  return 'processing';
}

/**
 * Estimates processing time based on stream duration
 */
function estimateProcessingTime(durationSeconds: number): number {
  // YouTube's processing time is roughly 1:1 to 1:3 ratio with video length
  // For live streams, it's typically on the higher end
  const durationMinutes = Math.floor(durationSeconds / 60);
  
  if (durationMinutes <= 30) {
    return 60; // 1 hour for short streams
  } else if (durationMinutes <= 120) {
    return 180; // 3 hours for medium streams
  } else {
    return 360; // 6 hours for long streams
  }
}

/**
 * Checks if an archived live stream is ready for replay
 * @param videoId - The YouTube video ID
 * @returns Promise<boolean>
 */
export async function isReplayReady(videoId: string): Promise<boolean> {
  try {
    if (!apiKey) {
      return false;
    }

    const videoDetails = await youtube.videos.list({
      key: apiKey,
      part: ["status"],
      id: [videoId],
    });

    if (!videoDetails.data.items || videoDetails.data.items.length === 0) {
      return false;
    }

    const video = videoDetails.data.items[0];
    const uploadStatus = video.status?.uploadStatus;
    const privacyStatus = video.status?.privacyStatus;
    
    return uploadStatus === 'processed' && privacyStatus === 'public';
    
  } catch (error) {
    console.error("Error checking replay status:", error);
    return false;
  }
}

/**
 * Gets all archived live streams (past live streams available for replay)
 * @param channelId - The YouTube channel ID
 * @param maxResults - Maximum number of results to return
 * @returns Promise<ArchivedLiveStream[]>
 */
export async function getArchivedLiveStreams(
  channelId: string,
  maxResults: number = 20
): Promise<ArchivedLiveStream[]> {
  try {
    if (!apiKey) {
      throw new Error("YouTube API key not configured");
    }

    // Search for videos that were live streams
    const searchResponse = await youtube.search.list({
      key: apiKey,
      part: ["snippet", "id"],
      channelId,
      type: ["video"],
      maxResults,
      order: "date",
    });

    if (!searchResponse.data.items || searchResponse.data.items.length === 0) {
      return [];
    }

    // Get detailed information for all videos
    const videoIds = searchResponse.data.items
      .map(item => item.id?.videoId)
      .filter(Boolean) as string[];

    const videoDetails = await youtube.videos.list({
      key: apiKey,
      part: ["snippet", "liveStreamingDetails", "statistics", "status", "contentDetails"],
      id: videoIds,
    });

    if (!videoDetails.data.items) {
      return [];
    }

    // Filter and process only past live streams
    const archivedStreams: ArchivedLiveStream[] = videoDetails.data.items
      .filter(video => {
        const liveDetails = video.liveStreamingDetails;
        return liveDetails?.actualStartTime && liveDetails?.actualEndTime;
      })
      .map(video => {
        const liveDetails = video.liveStreamingDetails!;
        const statistics = video.statistics;
        
        const startTime = new Date(liveDetails.actualStartTime!).getTime();
        const endTime = new Date(liveDetails.actualEndTime!).getTime();
        const streamDuration = Math.floor((endTime - startTime) / 1000);
        
        const processingStatus = determineProcessingStatus(video);
        
        return {
          id: video.id || "",
          title: video.snippet?.title || "",
          description: video.snippet?.description || "",
          thumbnail: video.snippet?.thumbnails?.high?.url || "",
          publishedAt: video.snippet?.publishedAt || "",
          channelTitle: video.snippet?.channelTitle || "",
          isLive: false,
          isPastLive: true,
          originalLiveStartTime: liveDetails.actualStartTime!,
          originalLiveEndTime: liveDetails.actualEndTime!,
          streamDuration,
          peakViewers: 0, // Would need to be stored separately
          averageViewers: 0, // Would need to be stored separately
          archivedAt: liveDetails.actualEndTime!,
          replayAvailable: processingStatus === 'completed',
          processingStatus,
          streamAnalytics: {
            totalViews: parseInt(statistics?.viewCount || "0", 10),
            likes: parseInt(statistics?.likeCount || "0", 10),
            comments: parseInt(statistics?.commentCount || "0", 10),
            shares: 0,
          },
        } as ArchivedLiveStream;
      });

    console.log(`📼 Found ${archivedStreams.length} archived live streams`);
    
    return archivedStreams;

  } catch (error) {
    console.error("Error getting archived live streams:", error);
    return [];
  }
}
