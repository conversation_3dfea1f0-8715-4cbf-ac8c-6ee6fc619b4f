import { NextResponse } from "next/server";
import { google } from "googleapis";
import { XMLParser } from "fast-xml-parser";
import { YouTubeVideo } from "@/types/youtube";

// Initialize YouTube API client
const youtube = google.youtube("v3");
const apiKey = process.env.YOUTUBE_API_KEY;

// YouTube RSS feed URL format
const YOUTUBE_RSS_URL = "https://www.youtube.com/feeds/videos.xml?channel_id=";

// Define route segment config for Next.js 15 caching
export const dynamic = "force-dynamic"; // Make this route dynamic by default
export const revalidate = 0; // Revalidate on every request

// Add interface for RSS feed entry
interface RSSFeedEntry {
  link: { "@_href": string };
  title: string;
  published: string;
  summary?: { _cdata?: string } | string;
  yt$state?: { name: string };
  author?: { name: string };
}

// Helper function to safely get text content from RSS entry
function getTextContent(
  content: string | { _cdata?: string } | undefined
): string {
  if (!content) return "";
  if (typeof content === "string") return content;
  return content._cdata || "";
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "20", 10);
    // Use RSS by default, only use API if explicitly requested
    const useApi = searchParams.get("api") === "true";

    // Get channel ID from environment variables
    const channelId = process.env.YOUTUBE_CHANNEL_ID;
    if (!channelId) {
      throw new Error("YouTube channel ID not configured");
    }

    console.log(`Fetching past sermons (page ${page}, limit ${limit})`);

    // Set cache control headers to allow caching for 1 hour
    const headers = new Headers();
    headers.set("Cache-Control", "public, max-age=3600, s-maxage=3600");
    headers.set("X-Response-Time", new Date().toISOString());

    let videos = [];
    let dataSource = "rss"; // Default to RSS

    // Always use RSS feed by default (no quota limits)
    console.log("Using RSS feed (default)");
    videos = await getVideosFromRSS(channelId);

    // Only try YouTube API if explicitly requested AND RSS failed or returned no videos
    if (useApi && videos.length === 0) {
      try {
        console.log(
          "RSS feed returned no videos, trying YouTube API as requested"
        );
        videos = await getVideosFromAPI(channelId, page, limit);
        dataSource = "api";
      } catch (apiError) {
        console.error("YouTube API error:", apiError);
        // We already tried RSS, so just log the error
      }
    }

    // Paginate the videos
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedVideos = videos.slice(startIndex, endIndex);

    return NextResponse.json(
      {
        videos: paginatedVideos,
        totalVideos: videos.length,
        totalPages: Math.ceil(videos.length / limit),
        currentPage: page,
        dataSource,
        timestamp: new Date().toISOString(),
      },
      { headers }
    );
  } catch (error) {
    console.error("Error in past sermons API:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch past sermons",
        errorDetails: error instanceof Error ? error.message : String(error),
        videos: [],
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * Fetches videos from YouTube API
 */
async function getVideosFromAPI(
  channelId: string,
  _page: number, // Prefix with underscore to indicate intentionally unused
  _limit: number // Prefix with underscore to indicate intentionally unused
) {
  if (!apiKey) {
    throw new Error("YouTube API key is not configured");
  }

  // Get videos from the channel
  const searchResponse = await youtube.search.list({
    key: apiKey,
    part: ["snippet", "id"],
    channelId,
    type: ["video"],
    maxResults: 50, // Maximum allowed by YouTube API
    order: "date",
  });

  if (!searchResponse.data.items || searchResponse.data.items.length === 0) {
    return [];
  }

  // Get detailed information about these videos
  const videoIds = searchResponse.data.items
    .map((item) => item.id?.videoId)
    .filter(Boolean) as string[];

  const videoDetailsResponse = await youtube.videos.list({
    key: apiKey,
    part: ["snippet", "liveStreamingDetails", "status"],
    id: videoIds,
  });

  if (!videoDetailsResponse.data.items) {
    return [];
  }

  // Process videos
  const processedVideos = videoDetailsResponse.data.items.map((video) => {
    // Check if this was a live stream that has ended
    const hasLiveStreamingDetails =
      video.liveStreamingDetails?.actualStartTime !== undefined &&
      video.liveStreamingDetails?.actualEndTime !== undefined;

    // Secondary indicators: title/description contains service-related keywords
    const title = video.snippet?.title?.toLowerCase() || "";
    const description = video.snippet?.description?.toLowerCase() || "";

    const titleHasServiceKeywords =
      title.includes("service") ||
      title.includes("sunday") ||
      title.includes("worship") ||
      title.includes("sermon") ||
      title.includes("live");

    const descriptionHasServiceKeywords =
      description.includes("service") ||
      description.includes("sunday") ||
      description.includes("worship") ||
      description.includes("sermon") ||
      description.includes("live stream");

    const isPastLive =
      hasLiveStreamingDetails ||
      titleHasServiceKeywords ||
      descriptionHasServiceKeywords;

    return {
      id: video.id || "",
      title: video.snippet?.title || "",
      description: video.snippet?.description || "",
      thumbnail: `https://i.ytimg.com/vi/${video.id}/hqdefault.jpg`,
      publishedAt: video.snippet?.publishedAt || "",
      channelTitle: video.snippet?.channelTitle || "",
      isLive: false, // These are past videos
      isPastLive,
      wasLive: hasLiveStreamingDetails,
    };
  });

  // Sort by published date (newest first)
  return processedVideos.sort(
    (a: YouTubeVideo, b: YouTubeVideo) =>
      new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
  );
}

/**
 * Fetches videos from YouTube RSS feed (no quota limits)
 */
async function getVideosFromRSS(channelId: string) {
  try {
    console.log("Fetching videos from RSS feed");

    // Fetch the RSS feed
    const response = await fetch(`${YOUTUBE_RSS_URL}${channelId}`, {
      next: { revalidate: 60 }, // Revalidate every minute
    });

    if (!response.ok) {
      throw new Error(
        `Failed to fetch RSS feed: ${response.status} ${response.statusText}`
      );
    }

    const xml = await response.text();

    // Parse the XML
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "@_",
    });

    const result = parser.parse(xml);

    if (
      !result.feed ||
      !result.feed.entry ||
      !Array.isArray(result.feed.entry)
    ) {
      console.warn("Invalid RSS feed format:", result);
      return [];
    }

    // Process the entries
    const videos = result.feed.entry
      .map((entry: RSSFeedEntry) => {
        // Extract video ID from the link
        const videoId = entry.link["@_href"].split("v=")[1];

        // Skip if video is deleted or private
        if (
          entry.yt$state?.name === "deleted" ||
          entry.yt$state?.name === "private" ||
          entry.yt$state?.name === "unlisted"
        ) {
          console.log(
            `Skipping deleted/private video from RSS: ${entry.title} (${entry.yt$state?.name})`
          );
          return null;
        }

        // Skip if the video is from the future (invalid date)
        const publishDate = new Date(entry.published);
        if (publishDate > new Date()) {
          console.log(
            `Skipping future-dated video: ${entry.title} (${entry.published})`
          );
          return null;
        }

        // Check if this might be a live stream or past live stream based on title
        const title = entry.title?.toLowerCase() || "";
        const description = getTextContent(entry.summary).toLowerCase();

        const isPotentialLiveStream =
          title.includes("live") ||
          title.includes("stream") ||
          title.includes("service") ||
          title.includes("sunday") ||
          title.includes("worship") ||
          title.includes("sermon") ||
          description.includes("live") ||
          description.includes("service") ||
          description.includes("sunday") ||
          description.includes("worship");

        // Get a reliable thumbnail URL (hqdefault always exists)
        const thumbnailUrl = `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`;

        return {
          id: videoId,
          title: entry.title || "",
          description: getTextContent(entry.summary),
          thumbnail: thumbnailUrl,
          publishedAt: entry.published,
          channelTitle: result.feed.title || entry.author?.name || "",
          isLive: false, // These are past videos
          isPastLive: isPotentialLiveStream,
          wasLive: isPotentialLiveStream,
        };
      })
      .filter((video): video is YouTubeVideo => video !== null);

    // Remove duplicates by ID first
    const uniqueByIdVideos = Array.from(
      new Map(videos.map((video: YouTubeVideo) => [video.id, video])).values()
    );

    // Now check for duplicate titles and keep only the most recent one
    const titleMap = new Map<string, YouTubeVideo>();
    uniqueByIdVideos.forEach((video: YouTubeVideo) => {
      const title = video.title;
      if (title) {
        // If we haven't seen this title before, or this video is newer than the one we've seen
        const existingVideo = titleMap.get(title);
        if (
          !existingVideo ||
          new Date(video.publishedAt) > new Date(existingVideo.publishedAt)
        ) {
          titleMap.set(title, video);
        }
      }
    });

    // Get the final list of unique videos (by both ID and title)
    const uniqueVideos = Array.from(titleMap.values());

    console.log(
      `RSS feed returned ${videos.length} videos, filtered to ${uniqueVideos.length} unique videos`
    );

    // Sort by published date (newest first)
    return uniqueVideos.sort((a: YouTubeVideo, b: YouTubeVideo) => {
      const dateA = new Date(a.publishedAt).getTime();
      const dateB = new Date(b.publishedAt).getTime();
      return dateB - dateA;
    });
  } catch (error) {
    console.error("Error fetching from RSS feed:", error);
    return []; // Return empty array on error
  }
}
