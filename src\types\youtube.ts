/**
 * Common YouTube types used across the application
 */

export interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  publishedAt: string;
  channelTitle: string;
  isLive?: boolean;
  isPastLive?: boolean;
  liveBroadcastContent?: "live" | "upcoming" | "none";
  wasLive?: boolean;
  verifiedLive?: boolean;
  concurrentViewers?: number;
}

export interface YouTubeApiResponse {
  isLive: boolean;
  liveStream: YouTubeVideo | null;
  recentPastLiveStream: YouTubeVideo | null;
  sermons: YouTubeVideo[];
  timestamp: string;
  cached?: boolean;
  dataSource?: 'api' | 'cache' | 'rss';
}
