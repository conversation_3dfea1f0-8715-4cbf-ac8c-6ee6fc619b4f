"use client";

import { usePathname } from "next/navigation";
import Head from "next/head";
import Script from "next/script";
import { BlogPosting, WithContext } from "schema-dts";
import { urlForImage } from "@/sanity/image";

interface BlogSEOProps {
  title: string;
  description: string;
  publishedAt: string;
  updatedAt?: string;
  image?: Record<string, unknown>;
  author?: {
    name: string;
    image?: Record<string, unknown>;
  };
  url?: string;
}

export function BlogSEO({
  title,
  description,
  publishedAt,
  updatedAt,
  image,
  author,
  url,
}: BlogSEOProps) {
  const pathname = usePathname();
  const baseUrl =
    process.env.NEXT_PUBLIC_BASE_URL || "https://revivalfiremissions.org";
  const canonicalUrl = url || `${baseUrl}${pathname}`;

  // Format dates for schema
  const publishedDate = new Date(publishedAt).toISOString();
  const modifiedDate = updatedAt
    ? new Date(updatedAt).toISOString()
    : publishedDate;

  // Prepare image URL
  const imageUrl = image
    ? urlForImage(image).width(1200).height(630).url()
    : `${baseUrl}/images/og-image.jpg`;

  // Create structured data for blog post
  const blogPostingSchema: WithContext<BlogPosting> = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    headline: title,
    description: description,
    image: imageUrl,
    datePublished: publishedDate,
    dateModified: modifiedDate,
    author: {
      "@type": "Person",
      name: author?.name || "Revival Fire Missions",
    },
    publisher: {
      "@type": "Organization",
      name: "Revival Fire Missions",
      logo: {
        "@type": "ImageObject",
        url: `${baseUrl}/logo/logo transparent[64].png`,
      },
    },
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": canonicalUrl,
    },
  };

  return (
    <>
      <Head>
        <link rel="canonical" href={canonicalUrl} />
      </Head>
      <Script
        id="blog-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(blogPostingSchema) }}
      />
    </>
  );
}
