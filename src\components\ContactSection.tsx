"use client";

import { useState } from "react";
import {
  // Mail,
  // Phone,
  MapPin,
  ExternalLink,
  Loader2,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { cn } from "@/lib/utils";

// Define the contact form schema with Zod
const contactFormSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  message: z.string().min(10, "Message must be at least 10 characters long"),
});

// Type for the contact form data
type ContactFormData = z.infer<typeof contactFormSchema>;

export function ContactSection() {
  // Form submission states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Initialize react-hook-form with zod validation
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      message: "",
    },
  });

  // Handle form submission
  const onSubmit = async (data: ContactFormData) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);

      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || "Failed to send message");
      }

      // Success
      setSubmitSuccess(true);
      reset(); // Clear the form

      // Reset success message after 5 seconds
      setTimeout(() => {
        setSubmitSuccess(false);
      }, 5000);
    } catch (error) {
      console.error("Contact form error:", error);
      setSubmitError(
        error instanceof Error
          ? error.message
          : "Failed to send message. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <section className="py-20 bg-secondary/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold mb-4">Get in Touch</h2>
          <p className="text-lg text-muted-foreground">
            We&apos;d love to hear from you
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-start">
          <div className="space-y-8">
            <div className="flex items-start gap-4">
              <div className="bg-primary/10 p-3 rounded-full">
                <MapPin className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-2">Visit Us</h3>
                <p className="text-muted-foreground">
                  Manna Mall Road, Off Kasama Rd
                  <br />
                  Lusaka, Zambia
                  <br />
                  <span className="text-xs">(Near Manna Shopping Centre)</span>
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  TIMES AND SEASONS CHURCH (REVIVAL FIRE MISSIONS INT&apos;L)
                  <br />
                  Hope Of Glory (Antioch Vision Centre)
                </p>
                <Link
                  href="https://maps.app.goo.gl/7hryXkaF1JJERCqF9"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 text-primary hover:underline mt-2"
                >
                  View on Google Maps
                  <ExternalLink className="h-4 w-4" />
                </Link>
              </div>
            </div>

            <div className="flex items-start gap-4">
              {/*<div className="bg-primary/10 p-3 rounded-full">
                <Phone className="h-6 w-6 text-primary" />
              </div>
               <div>
                <h3 className="text-xl font-semibold mb-2">Call Us</h3>
                <p className="text-muted-foreground">+260 97 1234567</p>
              </div> */}
            </div>

            <div className="flex items-start gap-4">
              {/*<div className="bg-primary/10 p-3 rounded-full">
                <Mail className="h-6 w-6 text-primary" />
              </div>
               <div>
                <h3 className="text-xl font-semibold mb-2">Email Us</h3>
                <p className="text-muted-foreground">
                  <EMAIL>
                </p>
                <Link
                  href="mailto:<EMAIL>"
                  className="flex items-center gap-1 text-primary hover:underline mt-2"
                >
                  Send Email
                  <Mail className="h-4 w-4" />
                </Link>
              </div> */}
            </div>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            {/* Success message */}
            {submitSuccess && (
              <div className="bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 p-4 rounded-lg flex items-start gap-3 mb-4">
                <CheckCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">Message sent successfully!</p>
                  <p className="text-sm">
                    Thank you for contacting us. We&apos;ll get back to you
                    soon.
                  </p>
                </div>
              </div>
            )}

            {/* Error message */}
            {submitError && (
              <div className="bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 p-4 rounded-lg flex items-start gap-3 mb-4">
                <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">Failed to send message</p>
                  <p className="text-sm">{submitError}</p>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <input
                  type="text"
                  placeholder="First Name"
                  className={cn(
                    "w-full p-3 rounded-lg border border-border bg-background",
                    errors.firstName && "border-red-500 dark:border-red-400"
                  )}
                  {...register("firstName")}
                  disabled={isSubmitting}
                />
                {errors.firstName && (
                  <p className="mt-1 text-sm text-red-500 dark:text-red-400">
                    {errors.firstName.message}
                  </p>
                )}
              </div>

              <div>
                <input
                  type="text"
                  placeholder="Last Name"
                  className={cn(
                    "w-full p-3 rounded-lg border border-border bg-background",
                    errors.lastName && "border-red-500 dark:border-red-400"
                  )}
                  {...register("lastName")}
                  disabled={isSubmitting}
                />
                {errors.lastName && (
                  <p className="mt-1 text-sm text-red-500 dark:text-red-400">
                    {errors.lastName.message}
                  </p>
                )}
              </div>
            </div>

            <div>
              <input
                type="email"
                placeholder="Email Address"
                className={cn(
                  "w-full p-3 rounded-lg border border-border bg-background",
                  errors.email && "border-red-500 dark:border-red-400"
                )}
                {...register("email")}
                disabled={isSubmitting}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-500 dark:text-red-400">
                  {errors.email.message}
                </p>
              )}
            </div>

            <div>
              <textarea
                placeholder="Your Message"
                rows={4}
                className={cn(
                  "w-full p-3 rounded-lg border border-border bg-background",
                  errors.message && "border-red-500 dark:border-red-400"
                )}
                {...register("message")}
                disabled={isSubmitting}
              />
              {errors.message && (
                <p className="mt-1 text-sm text-red-500 dark:text-red-400">
                  {errors.message.message}
                </p>
              )}
            </div>

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                "Send Message"
              )}
            </Button>
          </form>
        </div>

        {/* Google Maps Embed */}
        <div className="mt-16 space-y-4">
          <h3 className="text-xl font-semibold text-center">Our Location</h3>
          <div className="rounded-xl overflow-hidden shadow-lg border border-border h-[400px] w-full">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3845.3388053553335!2d28.321686675123352!3d-15.466197585129262!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x19408db833396b0b%3A0xed666c563292eb0e!2sTIMES%20AND%20SEASONS%20CHURCH%20(REVIVAL%20FIRE%20MISSIONS%20INT&#39;L)%20Hope%20Of%20Glory%20(Antioch%20Vision%20Centre!5e0!3m2!1sen!2szm!4v1745322802706!5m2!1sen!2szm"
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen={true}
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="Times and Seasons Church (Revival Fire Missions Int&#39;l) Location"
              aria-label="Google Maps showing Times and Seasons Church location"
            />
          </div>
          <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
            <a
              href="https://maps.app.goo.gl/7hryXkaF1JJERCqF9"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-primary text-white px-6 py-3 rounded-lg flex items-center gap-2 hover:bg-primary/90 transition-colors shadow-md"
            >
              <MapPin className="h-5 w-5" />
              Get Directions
            </a>

            <Link href="/churches">
              <Button size="lg" className="text-lg font-semibold px-6 py-3">
                View All Church Branches
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
