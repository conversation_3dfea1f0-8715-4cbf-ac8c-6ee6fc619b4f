import { Metadata } from "next";
import Link from "next/link";
import Image from "next/image";
import { getClient } from "@/sanity/client";
import { postsQuery } from "@/sanity/queries";
import { formatDistanceToNow } from "date-fns";
import { urlForImage } from "@/sanity/image";
import { generateBlogMetadata } from "@/lib/seo";
import { BlogListSEO } from "@/components/BlogListSEO";

export const revalidate = 3600; // Revalidate the data at most every hour

export const metadata: Metadata = generateBlogMetadata({
  title: "Blog",
  description:
    "Read the latest articles, teachings, and updates from Revival Fire Missions. Explore our spiritual insights and church news.",
});

interface SanityImage {
  _type: string;
  asset: {
    _ref: string;
    _type: string;
  };
}

interface Post {
  _id: string;
  title: string;
  slug: { current: string };
  mainImage: SanityImage | null;
  publishedAt: string;
  categories: string[];
  author: {
    name: string;
    image: SanityImage | null;
  };
  estimatedReadingTime: number;
}

export default async function BlogPage() {
  const posts = await getClient().fetch<Post[]>(postsQuery);

  return (
    <main className="min-h-screen">
      {/* Add structured data for SEO */}
      <BlogListSEO
        title="Revival Fire Missions Blog"
        description="Read the latest articles, teachings, and updates from Revival Fire Missions. Explore our spiritual insights and church news."
      />

      <section className="py-20 bg-secondary/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">Our Blog</h1>
            <p className="text-lg text-muted-foreground">
              Insights, teachings, and updates from Revival Fire Missions
            </p>
          </div>

          {posts.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-xl">No blog posts found.</p>
              <p className="text-muted-foreground mt-2">
                Check back soon for new content!
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post) => (
                <Link
                  key={post._id}
                  href={`/blog/${post.slug.current}`}
                  className="group"
                >
                  <article className="bg-background rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                    <div className="relative h-48">
                      {post.mainImage ? (
                        <Image
                          src={urlForImage(post.mainImage).url()}
                          alt={post.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                      ) : (
                        <div className="absolute inset-0 bg-primary/10 flex items-center justify-center">
                          <span className="text-primary">No image</span>
                        </div>
                      )}
                    </div>
                    <div className="p-6">
                      <div className="flex flex-wrap gap-2 mb-3">
                        {post.categories?.map((category) => (
                          <span
                            key={category}
                            className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full"
                          >
                            {category}
                          </span>
                        ))}
                      </div>
                      <h2 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                        {post.title}
                      </h2>
                      <div className="flex items-center justify-between text-sm text-muted-foreground mt-4">
                        <div className="flex items-center">
                          <span>
                            {post.publishedAt
                              ? formatDistanceToNow(
                                  new Date(post.publishedAt),
                                  {
                                    addSuffix: true,
                                  }
                                )
                              : "Date unknown"}
                          </span>
                        </div>
                        <span>{post.estimatedReadingTime || 3} min read</span>
                      </div>
                    </div>
                  </article>
                </Link>
              ))}
            </div>
          )}
        </div>
      </section>
    </main>
  );
}
