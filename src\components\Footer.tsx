"use client";

import { Facebook, Twitter, Instagram, Youtube, ArrowUp } from "lucide-react";
import { scrollToSection } from "@/lib/scroll";
import { Button } from "./ui/button";

export function Footer() {
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-xl font-bold mb-4">Revival Fire Missions</h3>
            <p className="text-primary-foreground/80">
              Igniting Hearts, Transforming Lives through the power of
              God&apos;s love.
            </p>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <a
                  href="#about"
                  className="hover:text-white transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    scrollToSection("about", 80);
                  }}
                >
                  About Us
                </a>
              </li>
              <li>
                <a
                  href="#ministries"
                  className="hover:text-white transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    scrollToSection("ministries", 80);
                  }}
                >
                  Ministries
                </a>
              </li>
              <li>
                <a
                  href="#events"
                  className="hover:text-white transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    scrollToSection("events", 80);
                  }}
                >
                  Events
                </a>
              </li>
              <li>
                <a
                  href="#sermons"
                  className="hover:text-white transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    scrollToSection("sermons", 80);
                  }}
                >
                  Sermons
                </a>
              </li>
              <li>
                <a
                  href="#contact"
                  className="hover:text-white transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    scrollToSection("contact", 80);
                  }}
                >
                  Contact
                </a>
              </li>
              <li>
                <a href="studio" className="hover:text-white transition-colors">
                  Studio
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-4">Service Times</h4>
            <ul className="space-y-2">
              <li>Sunday: 10:00 AM</li>
              <li>Wednesday: 7:00 PM</li>
              <li>Friday: 6:30 PM</li>
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-4">Connect With Us</h4>
            <div className="flex space-x-4">
              <a href="#" className="hover:text-white transition-colors">
                <Facebook className="h-6 w-6" />
              </a>
              <a href="#" className="hover:text-white transition-colors">
                <Twitter className="h-6 w-6" />
              </a>
              <a href="#" className="hover:text-white transition-colors">
                <Instagram className="h-6 w-6" />
              </a>
              <a href="#" className="hover:text-white transition-colors">
                <Youtube className="h-6 w-6" />
              </a>
            </div>
          </div>
        </div>

        <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center relative">
          <Button
            onClick={() => scrollToSection("home", 0)}
            className="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-primary-foreground text-primary hover:bg-primary-foreground/90 rounded-full p-2 shadow-lg"
            size="icon"
            aria-label="Back to top"
          >
            <ArrowUp className="h-5 w-5" />
          </Button>

          <p className="text-primary-foreground/60">
            © {new Date().getFullYear()} Revival Fire Missions. All rights
            reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
