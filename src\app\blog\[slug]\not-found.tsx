import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export default function BlogPostNotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-secondary/30">
      <div className="container px-4 py-16 text-center">
        <h1 className="text-4xl font-bold mb-4">Blog Post Not Found</h1>
        <p className="text-lg text-muted-foreground mb-8">
          The blog post you&apos;re looking for doesn&apos;t exist or has been
          removed.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild>
            <Link href="/blog">Return to Blog</Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/">Go to Homepage</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
