import { NextResponse } from "next/server";
import { getAllVideos } from "@/lib/youtube-simple";

// Define route segment config for Next.js 15 caching
export const dynamic = "force-dynamic"; // Make this route dynamic by default
export const revalidate = 0; // Revalidate on every request

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const debug = searchParams.get("debug") === "true";

    // Get channel ID from environment variables
    const channelId = process.env.YOUTUBE_CHANNEL_ID;
    if (!channelId) {
      throw new Error("YouTube channel ID not configured");
    }

    console.log("Fetching sermons with simplified API");
    console.log("Current server time:", new Date().toISOString());

    // Fetch all videos from YouTube
    const { videos, currentLiveStream, recentStream } =
      await getAllVideos(channelId);

    // Set cache control headers to prevent caching
    const headers = new Headers();
    headers.set(
      "Cache-Control",
      "no-store, no-cache, must-revalidate, proxy-revalidate"
    );
    headers.set("Pragma", "no-cache");
    headers.set("Expires", "0");
    headers.set("X-Response-Time", new Date().toISOString());

    // Prepare response data
    const responseData: any = {
      liveStream: currentLiveStream,
      recentlyEndedStream: recentStream,
      sermons: videos.filter((video) =>
        currentLiveStream ? video.id !== currentLiveStream.id : true
      ),
      timestamp: new Date().toISOString(),
    };

    // Add debug information if requested
    if (debug) {
      responseData.debug = {
        liveStreamFound: !!currentLiveStream,
        liveStreamTitle: currentLiveStream?.title || null,
        liveStreamDetails: currentLiveStream
          ? {
              id: currentLiveStream.id,
              title: currentLiveStream.title,
              publishedAt: currentLiveStream.publishedAt,
              isLive: currentLiveStream.isLive,
              isPastLive: currentLiveStream.isPastLive,
            }
          : null,
        recentlyEndedStreamFound: !!recentStream,
        recentlyEndedStreamTitle: recentStream?.title || null,
        recentlyEndedStreamDetails: recentStream
          ? {
              id: recentStream.id,
              title: recentStream.title,
              publishedAt: recentStream.publishedAt,
              publishedTimeAgo: `${Math.round(
                (Date.now() - new Date(recentStream.publishedAt).getTime()) /
                  (60 * 1000)
              )} minutes ago`,
              isLive: recentStream.isLive,
              isPastLive: recentStream.isPastLive,
            }
          : null,
        totalSermons: videos.length,
        pastLiveStreams: videos.filter((video) => video.isPastLive).length,
        pastLiveStreamTitles: videos
          .filter((video) => video.isPastLive)
          .slice(0, 3)
          .map((video) => video.title),
        serverTime: new Date().toISOString(),
        // Include the first few sermons for inspection
        recentSermons: videos.slice(0, 10).map((sermon) => ({
          id: sermon.id,
          title: sermon.title,
          publishedAt: sermon.publishedAt,
          publishedTimeAgo: `${Math.round(
            (Date.now() - new Date(sermon.publishedAt).getTime()) / (60 * 1000)
          )} minutes ago`,
          isLive: sermon.isLive,
          isPastLive: sermon.isPastLive,
        })),
      };
    }

    return NextResponse.json(responseData, { headers });
  } catch (error) {
    console.error("Error in sermons API:", error);

    // Provide specific error messages
    let errorMessage = "Failed to fetch sermons";
    let statusCode = 500;
    let errorDetails = null;

    if (error instanceof Error) {
      console.error("Error details:", error.message, error.stack);
      errorDetails = {
        message: error.message,
        stack: error.stack,
      };

      if (error.message.includes("quota")) {
        errorMessage = "YouTube API quota exceeded. Please try again later.";
        statusCode = 429; // Too Many Requests
      }
    }

    // Return error response
    return NextResponse.json(
      {
        error: errorMessage,
        errorDetails: errorDetails,
        timestamp: new Date().toISOString(),
        liveStream: null,
        sermons: [], // Return empty array instead of null
      },
      { status: statusCode }
    );
  }
}
