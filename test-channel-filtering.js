/**
 * Test script to verify channel filtering is working correctly
 * Run this to check if the live stream detection is properly filtering by channel ID
 */

const { getLiveStreamStatus } = require('./src/lib/youtube-live');

async function testChannelFiltering() {
  console.log('🧪 Testing Channel Filtering for Live Streams');
  console.log('='.repeat(50));
  
  const channelId = process.env.YOUTUBE_CHANNEL_ID || 'UCjYkOcueiCATTTJ5FoaxylA';
  console.log(`Testing with Channel ID: ${channelId}`);
  console.log('Expected Channel: Times and Seasons Church (Revival Fire Missions Int\'l)');
  console.log('');
  
  try {
    const result = await getLiveStreamStatus(channelId, true); // Force refresh
    
    console.log('📊 Live Stream Detection Results:');
    console.log(`Is Live: ${result.isLive}`);
    console.log(`Detection Method: ${result.analytics?.verificationMethod || 'unknown'}`);
    console.log(`API Calls Used: ${result.analytics?.apiCallsUsed || 0}`);
    console.log('');
    
    if (result.isLive && result.liveStream) {
      console.log('🔴 LIVE STREAM DETECTED:');
      console.log(`Title: ${result.liveStream.title}`);
      console.log(`Channel: ${result.liveStream.channelTitle}`);
      console.log(`Video ID: ${result.liveStream.id}`);
      console.log(`Viewers: ${result.liveStream.concurrentViewers || 'N/A'}`);
      console.log(`Published: ${result.liveStream.publishedAt}`);
      
      // Verify this is actually from your channel
      const videoUrl = `https://www.youtube.com/watch?v=${result.liveStream.id}`;
      console.log(`Video URL: ${videoUrl}`);
      
      // Check if the channel title matches what we expect
      const expectedChannelNames = [
        'Times and Seasons Church (Revival Fire Missions Int\'l)',
        'Times and Seasons Church',
        'Revival Fire Missions Int\'l',
        'Revival Fire Missions'
      ];
      
      const isCorrectChannel = expectedChannelNames.some(name => 
        result.liveStream.channelTitle.toLowerCase().includes(name.toLowerCase())
      );
      
      if (isCorrectChannel) {
        console.log('✅ CORRECT: This live stream belongs to your church channel');
      } else {
        console.log('❌ ERROR: This live stream belongs to a different channel!');
        console.log('This indicates the channel filtering is not working properly.');
      }
    } else {
      console.log('⚫ No live stream currently detected');
      console.log('This is expected if your church is not currently streaming');
    }
    
    console.log('');
    console.log('📋 Recent Videos from Channel:');
    if (result.recentVideos && result.recentVideos.length > 0) {
      result.recentVideos.slice(0, 3).forEach((video, index) => {
        console.log(`${index + 1}. ${video.title}`);
        console.log(`   Channel: ${video.channelTitle}`);
        console.log(`   Published: ${video.publishedAt}`);
        console.log('');
      });
    } else {
      console.log('No recent videos found');
    }
    
  } catch (error) {
    console.error('❌ Error testing channel filtering:', error.message);
    
    if (error.message.includes('quota')) {
      console.log('');
      console.log('💡 This error is likely due to YouTube API quota limits.');
      console.log('The channel filtering fix has been applied to the code.');
      console.log('Test again when the quota resets or during a live stream.');
    }
  }
  
  console.log('');
  console.log('🔧 Channel Filtering Fix Applied:');
  console.log('- Added explicit channel ID validation in live stream detection');
  console.log('- Videos from other channels will now be rejected');
  console.log('- Enhanced logging shows which channel each video belongs to');
  console.log('- This should prevent showing other YouTube live streams');
}

// Run the test
testChannelFiltering().catch(console.error);
