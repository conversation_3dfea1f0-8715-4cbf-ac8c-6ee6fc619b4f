/**
 * ENHANCED Test script to verify channel filtering is working correctly
 * Run this DURING your live stream to check if the filtering is working
 */

const { getLiveStreamStatus } = require("./src/lib/youtube-live");
const { getChannelLiveStreams } = require("./src/lib/youtube");

async function testChannelFiltering() {
  console.log("🧪 ENHANCED Channel Filtering Test for Live Streams");
  console.log("=".repeat(60));

  const channelId =
    process.env.YOUTUBE_CHANNEL_ID || "UCjYkOcueiCATTTJ5FoaxylA";
  console.log(`Testing with Channel ID: ${channelId}`);
  console.log(
    "Expected Channel: Times and Seasons Church (Revival Fire Missions Int'l)"
  );
  console.log("");
  console.log(
    "🚨 IMPORTANT: Run this test WHILE your church is live streaming!"
  );
  console.log("");

  try {
    console.log("📡 Testing Enhanced Live Stream Detection...");
    const result = await getLiveStreamStatus(channelId, true); // Force refresh

    console.log("📊 Enhanced Live Stream Detection Results:");
    console.log(`Is Live: ${result.isLive}`);
    console.log(
      `Detection Method: ${result.analytics?.verificationMethod || "unknown"}`
    );
    console.log(`API Calls Used: ${result.analytics?.apiCallsUsed || 0}`);
    console.log(
      `Detection Latency: ${result.analytics?.detectionLatency || 0}ms`
    );
    console.log("");

    if (result.isLive && result.liveStream) {
      console.log("🔴 LIVE STREAM DETECTED:");
      console.log(`Title: ${result.liveStream.title}`);
      console.log(`Channel: ${result.liveStream.channelTitle}`);
      console.log(`Video ID: ${result.liveStream.id}`);
      console.log(`Viewers: ${result.liveStream.concurrentViewers || "N/A"}`);
      console.log(`Published: ${result.liveStream.publishedAt}`);
      console.log(
        `Verification Level: ${result.liveStream.verificationLevel || "unknown"}`
      );

      // Verify this is actually from your channel
      const videoUrl = `https://www.youtube.com/watch?v=${result.liveStream.id}`;
      console.log(`Video URL: ${videoUrl}`);

      // Check if the channel title matches what we expect
      const expectedChannelNames = [
        "Times and Seasons Church (Revival Fire Missions Int'l)",
        "Times and Seasons Church",
        "Revival Fire Missions Int'l",
        "Revival Fire Missions",
      ];

      const isCorrectChannel = expectedChannelNames.some((name) =>
        result.liveStream.channelTitle
          .toLowerCase()
          .includes(name.toLowerCase())
      );

      if (isCorrectChannel) {
        console.log(
          "✅ CORRECT: This live stream belongs to your church channel"
        );
      } else {
        console.log(
          "❌ ERROR: This live stream belongs to a different channel!"
        );
        console.log("❌ CHANNEL FILTERING IS NOT WORKING PROPERLY!");
        console.log(`❌ Expected one of: ${expectedChannelNames.join(", ")}`);
        console.log(`❌ Got: ${result.liveStream.channelTitle}`);
      }
    } else {
      console.log("⚫ No live stream currently detected");
      console.log("");
      console.log(
        "💡 If your church is currently live streaming, this indicates:"
      );
      console.log("   1. The live stream detection is not working");
      console.log("   2. There might be an API issue");
      console.log(
        '   3. The stream might not be properly marked as "live" by YouTube'
      );
    }

    console.log("");
    console.log("🔍 Testing Direct YouTube API Live Streams...");

    try {
      const directLiveStreams = await getChannelLiveStreams(channelId, true);
      console.log(
        `Direct API returned ${directLiveStreams.length} live streams`
      );

      if (directLiveStreams.length > 0) {
        directLiveStreams.forEach((stream, index) => {
          console.log(`📺 Direct Live Stream ${index + 1}:`);
          console.log(`   Title: ${stream.title}`);
          console.log(`   Channel: ${stream.channelTitle}`);
          console.log(`   Video ID: ${stream.id}`);
          console.log(`   Verified Live: ${stream.verifiedLive || "unknown"}`);
          console.log("");
        });
      } else {
        console.log("   No live streams found via direct API");
      }
    } catch (directError) {
      console.log(`   Direct API Error: ${directError.message}`);
    }

    console.log("");
    console.log("📋 Recent Videos from Channel:");
    if (result.recentVideos && result.recentVideos.length > 0) {
      result.recentVideos.slice(0, 3).forEach((video, index) => {
        console.log(`${index + 1}. ${video.title}`);
        console.log(`   Channel: ${video.channelTitle}`);
        console.log(`   Published: ${video.publishedAt}`);
        console.log(`   Is Live: ${video.isLive || false}`);
        console.log(`   Was Live: ${video.isPastLive || false}`);
        console.log("");
      });
    } else {
      console.log("No recent videos found");
    }
  } catch (error) {
    console.error("❌ Error testing channel filtering:", error.message);

    if (error.message.includes("quota")) {
      console.log("");
      console.log("💡 This error is likely due to YouTube API quota limits.");
      console.log("The channel filtering fix has been applied to the code.");
      console.log("Test again when the quota resets or during a live stream.");
    }
  }

  console.log("");
  console.log("🔧 Enhanced Channel Filtering Fixes Applied:");
  console.log(
    "- ✅ Added explicit channel ID validation in live stream detection"
  );
  console.log(
    "- ✅ Videos from other channels will now be rejected with logging"
  );
  console.log(
    "- ✅ Enhanced logging shows which channel each video belongs to"
  );
  console.log(
    "- ✅ Added channel verification in both youtube.ts and youtube-live.ts"
  );
  console.log("- ✅ This should prevent showing other YouTube live streams");
  console.log("");
  console.log("📝 Next Steps:");
  console.log("1. Run this test DURING your next live stream");
  console.log(
    "2. Check the server logs for detailed channel filtering messages"
  );
  console.log('3. Look for "❌ NO - THIS IS THE PROBLEM!" messages in logs');
  console.log(
    "4. If other channels' streams still appear, check the logs for the source"
  );
}

// Run the test
testChannelFiltering().catch(console.error);
