/**
 * React hook for sermon search functionality
 * Manages search state, filters, pagination, and API calls
 */

'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { YouTubeVideo } from '@/lib/youtube-live';

export interface SearchFilters {
  dateRange?: {
    from?: string;
    to?: string;
  };
  sortBy?: 'relevance' | 'date' | 'title' | 'duration';
  sortOrder?: 'asc' | 'desc';
  category?: string;
  speaker?: string;
  minDuration?: number;
  maxDuration?: number;
}

export interface SearchResult {
  sermons: YouTubeVideo[];
  totalResults: number;
  totalPages: number;
  currentPage: number;
  searchQuery: string;
  filters: SearchFilters;
  suggestions: string[];
  searchTime: number;
}

export interface UseSermonSearchState {
  // Search state
  query: string;
  results: YouTubeVideo[];
  totalResults: number;
  totalPages: number;
  currentPage: number;
  suggestions: string[];
  
  // UI state
  isSearching: boolean;
  hasSearched: boolean;
  error: string | null;
  searchTime: number;
  
  // Filters
  filters: SearchFilters;
  
  // Actions
  setQuery: (query: string) => void;
  search: (newQuery?: string, newFilters?: SearchFilters) => Promise<void>;
  clearSearch: () => void;
  setPage: (page: number) => void;
  setFilters: (filters: Partial<SearchFilters>) => void;
  
  // Computed
  hasResults: boolean;
  hasFilters: boolean;
  isEmptySearch: boolean;
}

interface UseSermonSearchOptions {
  autoSearch?: boolean;
  debounceMs?: number;
  defaultFilters?: SearchFilters;
  onSearchComplete?: (results: SearchResult) => void;
  onError?: (error: string) => void;
}

export function useSermonSearch(options: UseSermonSearchOptions = {}): UseSermonSearchState {
  const {
    autoSearch = false,
    debounceMs = 300,
    defaultFilters = { sortBy: 'relevance', sortOrder: 'desc' },
    onSearchComplete,
    onError
  } = options;

  // State
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<YouTubeVideo[]>([]);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTime, setSearchTime] = useState(0);
  const [filters, setFiltersState] = useState<SearchFilters>(defaultFilters);

  // Debounced search effect
  useEffect(() => {
    if (!autoSearch || !query.trim()) return;

    const timeoutId = setTimeout(() => {
      search();
    }, debounceMs);

    return () => clearTimeout(timeoutId);
  }, [query, autoSearch, debounceMs]);

  // Search function
  const search = useCallback(async (newQuery?: string, newFilters?: SearchFilters) => {
    const searchQuery = newQuery !== undefined ? newQuery : query;
    const searchFilters = newFilters ? { ...filters, ...newFilters } : filters;
    
    if (!searchQuery.trim() && !hasActiveFilters(searchFilters)) {
      clearSearch();
      return;
    }

    setIsSearching(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      
      if (searchQuery.trim()) {
        params.append('q', searchQuery.trim());
      }
      
      params.append('page', currentPage.toString());
      params.append('limit', '12');
      
      if (searchFilters.sortBy) params.append('sortBy', searchFilters.sortBy);
      if (searchFilters.sortOrder) params.append('sortOrder', searchFilters.sortOrder);
      if (searchFilters.dateRange?.from) params.append('dateFrom', searchFilters.dateRange.from);
      if (searchFilters.dateRange?.to) params.append('dateTo', searchFilters.dateRange.to);
      if (searchFilters.category) params.append('category', searchFilters.category);
      if (searchFilters.speaker) params.append('speaker', searchFilters.speaker);
      if (searchFilters.minDuration) params.append('minDuration', searchFilters.minDuration.toString());
      if (searchFilters.maxDuration) params.append('maxDuration', searchFilters.maxDuration.toString());

      const response = await fetch(`/api/search-sermons?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`Search failed: ${response.status}`);
      }

      const data: SearchResult = await response.json();
      
      setResults(data.sermons);
      setTotalResults(data.totalResults);
      setTotalPages(data.totalPages);
      setCurrentPage(data.currentPage);
      setSuggestions(data.suggestions);
      setSearchTime(data.searchTime);
      setHasSearched(true);
      
      if (newQuery !== undefined) {
        setQuery(newQuery);
      }
      
      if (newFilters) {
        setFiltersState(prev => ({ ...prev, ...newFilters }));
      }

      onSearchComplete?.(data);
      
      console.log(`🔍 Search completed: "${searchQuery}" - ${data.totalResults} results in ${data.searchTime}ms`);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Search failed';
      setError(errorMessage);
      setResults([]);
      setTotalResults(0);
      setTotalPages(0);
      setSuggestions([]);
      onError?.(errorMessage);
      console.error('Search error:', err);
    } finally {
      setIsSearching(false);
    }
  }, [query, filters, currentPage, onSearchComplete, onError]);

  // Clear search
  const clearSearch = useCallback(() => {
    setQuery('');
    setResults([]);
    setTotalResults(0);
    setTotalPages(0);
    setCurrentPage(1);
    setSuggestions([]);
    setHasSearched(false);
    setError(null);
    setSearchTime(0);
    setFiltersState(defaultFilters);
  }, [defaultFilters]);

  // Set page
  const setPage = useCallback((page: number) => {
    setCurrentPage(page);
    // Trigger search with new page
    if (hasSearched) {
      search();
    }
  }, [hasSearched, search]);

  // Set filters
  const setFilters = useCallback((newFilters: Partial<SearchFilters>) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page when filters change
    
    // Trigger search if we have an active search
    if (hasSearched) {
      search(undefined, newFilters);
    }
  }, [hasSearched, search]);

  // Computed values
  const hasResults = useMemo(() => results.length > 0, [results.length]);
  const hasFilters = useMemo(() => hasActiveFilters(filters), [filters]);
  const isEmptySearch = useMemo(() => !query.trim() && !hasFilters, [query, hasFilters]);

  return {
    // Search state
    query,
    results,
    totalResults,
    totalPages,
    currentPage,
    suggestions,
    
    // UI state
    isSearching,
    hasSearched,
    error,
    searchTime,
    
    // Filters
    filters,
    
    // Actions
    setQuery,
    search,
    clearSearch,
    setPage,
    setFilters,
    
    // Computed
    hasResults,
    hasFilters,
    isEmptySearch,
  };
}

// Helper function to check if filters are active
function hasActiveFilters(filters: SearchFilters): boolean {
  return !!(
    filters.dateRange?.from ||
    filters.dateRange?.to ||
    filters.category ||
    filters.speaker ||
    filters.minDuration ||
    filters.maxDuration ||
    (filters.sortBy && filters.sortBy !== 'relevance') ||
    (filters.sortOrder && filters.sortOrder !== 'desc')
  );
}

// Export types for external use
export type { SearchResult, SearchFilters };
