import { defineField, defineType } from "sanity";

export const branchType = defineType({
  name: "branch",
  title: "Church Branch",
  type: "document",
  fields: [
    defineField({
      name: "name",
      title: "Name",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "address",
      title: "Address",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineF<PERSON>({ name: "note", title: "Note", type: "string" }),
    define<PERSON><PERSON>({ name: "mapUrl", title: "Google Maps Link", type: "url" }),
    defineField({
      name: "embedUrl",
      title: "Google Maps Embed URL",
      type: "url",
    }),
    defineField({ name: "email", title: "Email", type: "string" }),
    defineField({ name: "contact", title: "Contact", type: "string" }),
    define<PERSON>ield({
      name: "contact<PERSON><PERSON>",
      title: "Contact Person",
      type: "string",
    }),
    define<PERSON><PERSON>({
      name: "hours",
      title: "Operating Hours",
      type: "object",
      fields: [
        { name: "Sunday", type: "string", title: "Sunday" },
        { name: "Monday", type: "string", title: "Monday" },
        { name: "Tuesday", type: "string", title: "Tuesday" },
        { name: "Wednesday", type: "string", title: "Wednesday" },
        { name: "Thursday", type: "string", title: "Thursday" },
        { name: "Friday", type: "string", title: "Friday" },
        { name: "Saturday", type: "string", title: "Saturday" },
      ],
    }),
    defineField({
      name: "meetings",
      title: "Meetings",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            { name: "title", type: "string", title: "Title" },
            { name: "day", type: "string", title: "Day" },
            { name: "time", type: "string", title: "Time" },
            { name: "description", type: "string", title: "Description" },
          ],
        },
      ],
    }),
  ],
  preview: {
    select: { title: "name", subtitle: "address" },
  },
});
