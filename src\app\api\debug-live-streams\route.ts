/**
 * Debug endpoint to help identify why other channels' live streams are showing
 * Call this endpoint during your live stream to see detailed debugging info
 */

import { NextResponse } from "next/server";
import { getLiveStreamStatus } from "@/lib/youtube-live";
import { getChannelLiveStreams } from "@/lib/youtube";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const forceRefresh = searchParams.get("force") === "true";
    
    const channelId = process.env.YOUTUBE_CHANNEL_ID;
    if (!channelId) {
      return NextResponse.json({ error: "Channel ID not configured" }, { status: 500 });
    }

    console.log("🔍 DEBUG: Starting comprehensive live stream debugging");
    console.log(`🔍 DEBUG: Channel ID: ${channelId}`);
    console.log(`🔍 DEBUG: Expected Channel: Times and Seasons Church (Revival Fire Missions Int'l)`);

    const debugInfo: any = {
      timestamp: new Date().toISOString(),
      channelId,
      expectedChannelName: "Times and Seasons Church (Revival Fire Missions Int'l)",
      tests: {}
    };

    // Test 1: Enhanced Live Stream Detection
    console.log("🔍 DEBUG: Testing enhanced live stream detection...");
    try {
      const enhancedResult = await getLiveStreamStatus(channelId, forceRefresh);
      debugInfo.tests.enhancedDetection = {
        success: true,
        isLive: enhancedResult.isLive,
        liveStream: enhancedResult.liveStream ? {
          id: enhancedResult.liveStream.id,
          title: enhancedResult.liveStream.title,
          channelTitle: enhancedResult.liveStream.channelTitle,
          channelId: enhancedResult.liveStream.channelId || 'unknown',
          verificationLevel: enhancedResult.liveStream.verificationLevel,
          concurrentViewers: enhancedResult.liveStream.concurrentViewers,
          publishedAt: enhancedResult.liveStream.publishedAt
        } : null,
        analytics: enhancedResult.analytics,
        cached: enhancedResult.cached
      };

      // Check if the detected stream belongs to the correct channel
      if (enhancedResult.isLive && enhancedResult.liveStream) {
        const expectedChannelNames = [
          'Times and Seasons Church (Revival Fire Missions Int\'l)',
          'Times and Seasons Church',
          'Revival Fire Missions Int\'l',
          'Revival Fire Missions'
        ];
        
        const isCorrectChannel = expectedChannelNames.some(name => 
          enhancedResult.liveStream!.channelTitle.toLowerCase().includes(name.toLowerCase())
        );
        
        debugInfo.tests.enhancedDetection.channelValidation = {
          isCorrectChannel,
          detectedChannelTitle: enhancedResult.liveStream.channelTitle,
          expectedChannelNames
        };

        if (!isCorrectChannel) {
          debugInfo.tests.enhancedDetection.ERROR = "WRONG CHANNEL DETECTED!";
        }
      }
    } catch (error) {
      debugInfo.tests.enhancedDetection = {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }

    // Test 2: Direct YouTube API Live Streams
    console.log("🔍 DEBUG: Testing direct YouTube API live streams...");
    try {
      const directStreams = await getChannelLiveStreams(channelId, forceRefresh);
      debugInfo.tests.directAPI = {
        success: true,
        streamCount: directStreams.length,
        streams: directStreams.map(stream => ({
          id: stream.id,
          title: stream.title,
          channelTitle: stream.channelTitle,
          verifiedLive: stream.verifiedLive,
          liveBroadcastContent: stream.liveBroadcastContent
        }))
      };

      // Check each stream for channel validation
      if (directStreams.length > 0) {
        debugInfo.tests.directAPI.channelValidation = directStreams.map(stream => {
          const expectedChannelNames = [
            'Times and Seasons Church (Revival Fire Missions Int\'l)',
            'Times and Seasons Church',
            'Revival Fire Missions Int\'l',
            'Revival Fire Missions'
          ];
          
          const isCorrectChannel = expectedChannelNames.some(name => 
            stream.channelTitle.toLowerCase().includes(name.toLowerCase())
          );
          
          return {
            streamTitle: stream.title,
            channelTitle: stream.channelTitle,
            isCorrectChannel,
            videoId: stream.id
          };
        });

        const wrongChannelStreams = debugInfo.tests.directAPI.channelValidation.filter(
          (validation: any) => !validation.isCorrectChannel
        );

        if (wrongChannelStreams.length > 0) {
          debugInfo.tests.directAPI.ERROR = "WRONG CHANNEL STREAMS DETECTED!";
          debugInfo.tests.directAPI.wrongChannelStreams = wrongChannelStreams;
        }
      }
    } catch (error) {
      debugInfo.tests.directAPI = {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }

    // Summary
    debugInfo.summary = {
      totalIssuesFound: 0,
      issues: []
    };

    if (debugInfo.tests.enhancedDetection?.ERROR) {
      debugInfo.summary.totalIssuesFound++;
      debugInfo.summary.issues.push("Enhanced detection found wrong channel stream");
    }

    if (debugInfo.tests.directAPI?.ERROR) {
      debugInfo.summary.totalIssuesFound++;
      debugInfo.summary.issues.push("Direct API found wrong channel streams");
    }

    if (debugInfo.summary.totalIssuesFound === 0) {
      debugInfo.summary.status = "✅ All tests passed - channel filtering is working correctly";
    } else {
      debugInfo.summary.status = "❌ Issues found - channel filtering needs investigation";
    }

    console.log("🔍 DEBUG: Debugging complete");
    console.log(`🔍 DEBUG: Issues found: ${debugInfo.summary.totalIssuesFound}`);

    return NextResponse.json(debugInfo, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error("🔍 DEBUG: Error in debug endpoint:", error);
    
    return NextResponse.json({
      error: "Debug endpoint failed",
      message: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
