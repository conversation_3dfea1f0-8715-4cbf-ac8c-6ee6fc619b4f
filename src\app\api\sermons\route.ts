import { NextResponse } from "next/server";
import { formatDistanceToNow } from "date-fns";
import {
  getChannelLiveStreams,
  getRecent<PERSON>er<PERSON>,
  invalidateCache,
  type YouTubeVideo,
} from "@/lib/youtube";
import {
  getChannelVideosFromRss,
  detectLiveStreamFromRss,
} from "@/lib/youtubeRss";

// Helper function to create a promise that rejects after a timeout
function createTimeoutPromise(ms: number): {
  promise: Promise<never>;
  clear: () => void;
} {
  let timeoutId: NodeJS.Timeout;

  const promise = new Promise<never>((_, reject) => {
    timeoutId = setTimeout(() => {
      reject(new Error(`Request timed out after ${ms}ms`));
    }, ms);
  });

  const clear = () => {
    clearTimeout(timeoutId);
  };

  return { promise, clear };
}

// Define route segment config for Next.js 15 caching
export const dynamic = "force-dynamic"; // Make this route dynamic by default
export const revalidate = 0; // Revalidate on every request to ensure fresh data

export async function GET(request: Request) {
  // Create a timeout that we can clear later
  const timeout = createTimeoutPromise(30000); // 30 seconds timeout

  try {
    const { searchParams } = new URL(request.url);
    const refresh = searchParams.get("refresh") === "true";
    const debug = searchParams.get("debug") === "true";
    const forceRefresh = searchParams.get("force") === "true"; // Force refresh bypasses all caching

    const channelId = process.env.YOUTUBE_CHANNEL_ID;
    if (!channelId) {
      throw new Error("YouTube channel ID not configured");
    }

    // If force refresh is requested, invalidate all caches including RSS
    if (forceRefresh) {
      console.log(
        "Force refresh requested, invalidating ALL caches including RSS"
      );
      console.log("Current server time:", new Date().toISOString());

      // Invalidate YouTube API caches
      invalidateCache(channelId, "all");

      // Also invalidate RSS feed cache by directly accessing the cache module
      // This ensures we get completely fresh data
      const rssKey = `rss-videos-${channelId}`;
      if (cache.has(rssKey)) {
        cache.delete(rssKey);
        console.log("Invalidated RSS feed cache");
      }
    }
    // If regular refresh parameter is provided, invalidate just the YouTube API cache
    else if (refresh) {
      console.log("Refresh requested, invalidating YouTube API cache");
      invalidateCache(channelId, "all");
    }

    // First try to get data from RSS feed (no quota limits)
    let liveStreams: YouTubeVideo[] = [];
    let sermons: YouTubeVideo[] = [];
    let usingRssFeed = false;
    let usingApiCache = false;

    try {
      // Try to get videos from RSS feed first
      console.log("Attempting to fetch data from YouTube RSS feed...");
      console.log("Current server time:", new Date().toISOString());
      const rssVideos = await getChannelVideosFromRss(channelId);

      if (rssVideos.length > 0) {
        console.log(
          `Successfully fetched ${rssVideos.length} videos from RSS feed`
        );
        sermons = rssVideos;
        usingRssFeed = true;

        // Try to detect live stream from RSS
        const potentialLiveStream = await detectLiveStreamFromRss(channelId);
        if (potentialLiveStream) {
          liveStreams = [potentialLiveStream];
          console.log("Potential live stream detected from RSS feed");
        }
      } else {
        console.log("No videos found in RSS feed, falling back to YouTube API");

        // Fall back to YouTube API if RSS feed fails or returns no results
        // Race between the API calls and the timeout
        try {
          [liveStreams, sermons] = (await Promise.race([
            Promise.all([
              getChannelLiveStreams(channelId, forceRefresh).catch((error) => {
                console.error("Error fetching live streams:", error);
                return [] as YouTubeVideo[];
              }),
              getRecentSermons(channelId).catch((error) => {
                console.error("Error fetching sermons:", error);
                return [] as YouTubeVideo[];
              }),
            ]),
            timeout.promise,
          ])) as [YouTubeVideo[], YouTubeVideo[]];

          // Check if we're using cached data
          usingApiCache = sermons.length > 0;
        } catch (error) {
          console.error("Error in API race condition:", error);
          // Continue with empty arrays if there's an error
        }
      }
    } catch (error) {
      console.error("Error fetching from RSS and API:", error);
      // Continue with empty arrays if there's an error
    }

    // Clear the timeout since the API calls completed successfully
    timeout.clear();

    // Current live stream (if any)
    // ONLY use verified live streams (from the videos.list API)
    let currentLiveStream =
      liveStreams.find((stream) => stream.verifiedLive) || null;

    if (currentLiveStream) {
      console.log("Using verified live stream:", currentLiveStream.title);
    } else {
      console.log("No verified live streams found");

      // Log any streams that were marked as live by YouTube but not verified
      const nonVerifiedLiveStreams = liveStreams.filter(
        (stream) =>
          stream.liveBroadcastContent === "live" && !stream.verifiedLive
      );

      if (nonVerifiedLiveStreams.length > 0) {
        console.log(
          `Found ${nonVerifiedLiveStreams.length} streams marked as live by YouTube but not verified:`,
          nonVerifiedLiveStreams.map((s) => s.title).join(", ")
        );
      }
    }

    // Special check for videos that have ended recently
    // If we have a recent video that has live indicators but is not marked as live,
    // it might be a stream that has ended
    let recentlyEndedStream = null;

    // First, check if we have any videos from the last 24 hours
    // These are most likely to be recently ended streams
    if (sermons.length > 0) {
      // Sort sermons by publishedAt date (most recent first)
      const sortedSermons = [...sermons].sort(
        (a, b) =>
          new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
      );

      // Get the most recent sermon
      const mostRecentSermon = sortedSermons[0];
      const publishTime = new Date(mostRecentSermon.publishedAt).getTime();
      const twentyFourHoursAgo = Date.now() - 24 * 60 * 60 * 1000;

      // If the most recent sermon is from the last 24 hours, it's likely a recently ended stream
      if (publishTime > twentyFourHoursAgo) {
        const minutesAgo = Math.round((Date.now() - publishTime) / (60 * 1000));
        const hoursAgo = Math.round(minutesAgo / 60);

        console.log(
          `Most recent sermon was published ${hoursAgo} hours ago (${minutesAgo} minutes): ${mostRecentSermon.title}`
        );

        // Check if it has live indicators in the title or description
        const title = mostRecentSermon.title.toLowerCase();
        const description = mostRecentSermon.description.toLowerCase();

        const hasLiveIndicator =
          title.includes("live") ||
          title.includes("stream") ||
          title.includes("service") ||
          title.includes("worship") ||
          title.includes("sunday") ||
          description.includes("live") ||
          description.includes("stream") ||
          description.includes("service") ||
          description.includes("worship");

        // If it has live indicators or is very recent (< 2 hours), consider it a recently ended stream
        if (hasLiveIndicator || minutesAgo < 120) {
          console.log(
            "Found a recent video that was likely a live stream:",
            mostRecentSermon.title
          );
          recentlyEndedStream = mostRecentSermon;
        }
      }
    }

    // If we didn't find a very recent stream, check for any sermon marked as wasLive
    if (!recentlyEndedStream && !currentLiveStream && sermons.length > 0) {
      // Find all sermons that were marked as live
      const pastLiveSermons = sermons.filter((sermon) => sermon.wasLive);

      if (pastLiveSermons.length > 0) {
        // Sort by most recent first
        pastLiveSermons.sort(
          (a, b) =>
            new Date(b.publishedAt).getTime() -
            new Date(a.publishedAt).getTime()
        );

        console.log(
          `Found ${pastLiveSermons.length} past live sermons, using the most recent one:`,
          pastLiveSermons[0].title
        );

        // Store the most recent past live stream
        recentlyEndedStream = pastLiveSermons[0];
      } else {
        // If no sermons were marked as wasLive, fall back to checking for live indicators
        // in sermons from the last 7 days
        const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;

        const recentSermons = sermons.filter((sermon) => {
          const publishTime = new Date(sermon.publishedAt).getTime();
          return publishTime > sevenDaysAgo;
        });

        if (recentSermons.length > 0) {
          // Sort by most recent first
          recentSermons.sort(
            (a, b) =>
              new Date(b.publishedAt).getTime() -
              new Date(a.publishedAt).getTime()
          );

          // Check the most recent sermon for live indicators
          const mostRecent = recentSermons[0];
          const title = mostRecent.title.toLowerCase();
          const description = mostRecent.description.toLowerCase();

          const hasLiveIndicator =
            title.includes("live") ||
            title.includes("stream") ||
            title.includes("service") ||
            title.includes("worship") ||
            title.includes("sunday") ||
            description.includes("live") ||
            description.includes("stream");

          if (hasLiveIndicator) {
            console.log(
              "Found a recent sermon with live indicators:",
              mostRecent.title
            );
            recentlyEndedStream = mostRecent;
          }
        }
      }
    }

    // DISABLED: Automatic detection of live streams from recent videos
    // We now ONLY trust the verified live streams from the YouTube API
    if (!currentLiveStream && sermons.length > 0) {
      console.log(
        "No verified live stream found. We will NOT attempt to detect live streams from recent videos."
      );

      // Log the most recent sermon for reference
      if (sermons.length > 0) {
        const mostRecentSermon = [...sermons].sort(
          (a, b) =>
            new Date(b.publishedAt).getTime() -
            new Date(a.publishedAt).getTime()
        )[0];

        const publishTime = new Date(mostRecentSermon.publishedAt).getTime();
        const minutesAgo = Math.round((Date.now() - publishTime) / (60 * 1000));

        console.log(
          `Most recent sermon was published ${minutesAgo} minutes ago: "${mostRecentSermon.title}"`
        );
        console.log(
          `This sermon is NOT being shown as live, even if YouTube incorrectly marks it as live.`
        );
      }
    }

    // Include all sermons, including past live streams, but exclude current live stream
    let allSermons = sermons.filter((sermon) =>
      currentLiveStream ? sermon.id !== currentLiveStream.id : true
    );

    // Remove duplicate titles, keeping only the most recent one
    console.log("Checking for duplicate sermon titles...");
    const titleMap = new Map();
    const duplicateTitles = new Set();

    allSermons.forEach((sermon) => {
      const existingSermon = titleMap.get(sermon.title);
      if (existingSermon) {
        // Found a duplicate title
        duplicateTitles.add(sermon.title);

        const existingDate = new Date(existingSermon.publishedAt);
        const currentDate = new Date(sermon.publishedAt);

        console.log(`Found duplicate title: "${sermon.title}"`);
        console.log(
          `  - Existing: ${existingDate.toISOString()} (${formatDistanceToNow(existingDate, { addSuffix: true })})`
        );
        console.log(
          `  - Current:  ${currentDate.toISOString()} (${formatDistanceToNow(currentDate, { addSuffix: true })})`
        );

        // Keep the most recent one
        if (currentDate > existingDate) {
          console.log(
            `  - Keeping the newer one (${formatDistanceToNow(currentDate, { addSuffix: true })})`
          );
          titleMap.set(sermon.title, sermon);
        } else {
          console.log(
            `  - Keeping the existing one (${formatDistanceToNow(existingDate, { addSuffix: true })})`
          );
        }
      } else {
        // First time seeing this title
        titleMap.set(sermon.title, sermon);
      }
    });

    // Get the final list of unique sermons by title
    allSermons = Array.from(titleMap.values());

    // Sort by publishedAt date (most recent first)
    allSermons.sort(
      (a, b) =>
        new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
    );

    console.log(
      `After removing duplicates: ${allSermons.length} unique sermons`
    );

    // Set cache control headers for Next.js 15 with no caching to ensure fresh data
    const headers = new Headers();
    headers.set(
      "Cache-Control",
      "no-store, no-cache, must-revalidate, proxy-revalidate"
    );
    // Add Vercel-specific headers to prevent caching
    headers.set("CDN-Cache-Control", "no-store");
    headers.set("Vercel-CDN-Cache-Control", "no-store");
    // Add timestamp header to help with debugging
    headers.set("X-Response-Time", new Date().toISOString());
    // Add Pragma header for older browsers
    headers.set("Pragma", "no-cache");
    // Set Expires header to the past
    headers.set("Expires", "0");

    // Prepare response data
    const responseData: any = {
      liveStream: currentLiveStream,
      recentlyEndedStream: recentlyEndedStream, // Include the recently ended stream
      sermons: allSermons,
      cached: !refresh,
      dataSource: usingRssFeed ? "rss" : usingApiCache ? "api-cached" : "api",
      timestamp: new Date().toISOString(),
    };

    // Add debug information if requested
    if (debug) {
      responseData.debug = {
        liveStreamFound: !!currentLiveStream,
        liveStreamSource: currentLiveStream
          ? liveStreams.length > 0
            ? "direct_detection"
            : "recent_sermon_detection"
          : null,
        liveStreamTitle: currentLiveStream?.title || null,
        recentlyEndedStreamFound: !!recentlyEndedStream,
        recentlyEndedStreamTitle: recentlyEndedStream?.title || null,
        recentlyEndedStreamTime: recentlyEndedStream
          ? new Date(recentlyEndedStream.publishedAt).toISOString()
          : null,
        totalSermons: sermons.length,
        usingRssFeed,
        usingApiCache,
        serverTime: new Date().toISOString(),
        // Information about duplicate titles
        duplicateTitlesFound: duplicateTitles.size > 0,
        duplicateTitlesCount: duplicateTitles.size,
        duplicateTitles: Array.from(duplicateTitles),
        // Information about past live streams
        pastLiveStreams: sermons.filter((sermon) => sermon.wasLive).length,
        pastLiveStreamTitles: sermons
          .filter((sermon) => sermon.wasLive)
          .slice(0, 3)
          .map((s) => s.title),

        // Include the first few sermons for inspection
        recentSermons: sermons.slice(0, 10).map((sermon) => ({
          id: sermon.id,
          title: sermon.title,
          publishedAt: sermon.publishedAt,
          publishedTimeAgo: `${Math.round((Date.now() - new Date(sermon.publishedAt).getTime()) / (60 * 1000))} minutes ago`,
          liveBroadcastContent: sermon.liveBroadcastContent,
          wasLive: sermon.wasLive,
          liveIndicators: {
            titleHasLive: sermon.title.toLowerCase().includes("live"),
            titleHasStream: sermon.title.toLowerCase().includes("stream"),
            titleHasService: sermon.title.toLowerCase().includes("service"),
            titleHasWorship: sermon.title.toLowerCase().includes("worship"),
            descHasLive: sermon.description.toLowerCase().includes("live"),
          },
        })),
        requestInfo: {
          refresh: refresh,
          forceRefresh: forceRefresh,
          debug: debug,
          url: request.url,
        },
      };
    }

    return NextResponse.json(responseData, { headers });
  } catch (error) {
    console.error("Error in sermons API:", error);

    // Clean up timeout
    timeout.clear();

    // Provide more specific error messages based on the error type
    let errorMessage = "Failed to fetch sermons";
    let statusCode = 500;
    let errorDetails = null;

    if (error instanceof Error) {
      console.error("Error details:", error.message, error.stack);
      errorDetails = {
        message: error.message,
        stack: error.stack,
      };

      if (error.message.includes("timed out")) {
        errorMessage = "YouTube API request timed out. Please try again later.";
        statusCode = 504; // Gateway Timeout
      } else if (error.message.includes("quota")) {
        errorMessage = "YouTube API quota exceeded. Please try again later.";
        statusCode = 429; // Too Many Requests
      } else if (error.message.includes("configured")) {
        errorMessage = error.message;
        statusCode = 400; // Bad Request
      }
    }

    // Try to return some data even if there's an error
    try {
      // Return a fallback response with error information
      return NextResponse.json(
        {
          error: errorMessage,
          errorDetails: errorDetails,
          timestamp: new Date().toISOString(),
          liveStream: null,
          sermons: [], // Return empty array instead of null
          cached: false,
          dataSource: "error-fallback",
        },
        { status: statusCode }
      );
    } catch (fallbackError) {
      console.error("Error creating fallback response:", fallbackError);
      // Last resort simple error response
      return NextResponse.json(
        { error: "Internal server error", timestamp: new Date().toISOString() },
        { status: 500 }
      );
    }
  }
}
