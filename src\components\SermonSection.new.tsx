"use client";

import { useState, useEffect, useMemo } from "react";
import {
  Play,
  Share2,
  X,
  Dot,
  ChevronLeft,
  ChevronRight,
  RefreshCw,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import type { YouTubeVideo } from "@/lib/youtube";
import { formatDistanceToNow } from "date-fns";
import { generateChurchVideoEmbedUrl } from "@/lib/youtube-embed";

// Extended AbortController interface with timeoutId property
interface ExtendedAbortController extends AbortController {
  timeoutId?: NodeJS.Timeout;
}

export function SermonSection() {
  const [sermons, setSermons] = useState<YouTubeVideo[]>([]);
  const [liveStream, setLiveStream] = useState<YouTubeVideo | null>(null);
  const [selectedSermon, setSelectedSermon] = useState<YouTubeVideo | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);
  const [isCached, setIsCached] = useState(true);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const sermonsPerPage = 6; // 2 rows of 3 sermons each

  // Calculate current sermons to display
  const indexOfLastSermon = currentPage * sermonsPerPage;
  const indexOfFirstSermon = indexOfLastSermon - sermonsPerPage;
  const currentSermons = useMemo(() => {
    return sermons.slice(indexOfFirstSermon, indexOfLastSermon);
  }, [sermons, indexOfFirstSermon, indexOfLastSermon]);

  // Calculate total pages
  const totalPages = useMemo(() => {
    return Math.ceil(sermons.length / sermonsPerPage);
  }, [sermons.length, sermonsPerPage]);

  // Change page
  const paginate = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    // Scroll to top of sermon section
    window.scrollTo({
      top: document.getElementById("sermons-section")?.offsetTop || 0,
      behavior: "smooth",
    });
  };

  // Go to next page
  const nextPage = () => {
    if (currentPage < totalPages) {
      paginate(currentPage + 1);
    }
  };

  // Go to previous page
  const prevPage = () => {
    if (currentPage > 1) {
      paginate(currentPage - 1);
    }
  };

  // Function to fetch sermons with optional refresh
  const fetchSermons = async (refresh = false) => {
    try {
      setError(null);
      if (refresh) {
        setIsRefreshing(true);
      }

      // Use a timeout to prevent hanging requests
      const fetchWithTimeout = async (url: string, timeoutMs = 10000) => {
        // Create a controller for this specific request
        const controller = new AbortController() as ExtendedAbortController;

        // Create a promise that resolves after the timeout
        const timeoutPromise = new Promise((_, reject) => {
          const timeoutId = setTimeout(() => {
            controller.abort();
            reject(new Error("Request timed out"));
          }, timeoutMs);

          // Store the timeout ID on the controller for cleanup
          controller.timeoutId = timeoutId;
        });

        try {
          // Race between the fetch and the timeout
          const response = (await Promise.race([
            fetch(url, { signal: controller.signal }),
            timeoutPromise,
          ])) as Response;

          // Clear the timeout if fetch wins the race
          if (controller.timeoutId) {
            clearTimeout(controller.timeoutId);
            controller.timeoutId = undefined;
          }

          return response;
        } catch (error) {
          // Clear the timeout if it hasn't fired yet
          if (controller.timeoutId) {
            clearTimeout(controller.timeoutId);
            controller.timeoutId = undefined;
          }

          // Rethrow with a more specific message for AbortError
          if (error instanceof DOMException && error.name === "AbortError") {
            throw new Error("Request was aborted due to timeout");
          }
          throw error;
        }
      };

      const url = refresh ? "/api/sermons?refresh=true" : "/api/sermons";

      // Try to fetch with a timeout
      const response = await fetchWithTimeout(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch sermons: ${response.status}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      // Only update state if we have valid data
      if (Array.isArray(data.sermons)) {
        setSermons(data.sermons);
        setLiveStream(data.liveStream);
        setIsCached(data.cached);
        setLastUpdated(data.timestamp);

        // Reset to first page when new sermons are loaded
        setCurrentPage(1);

        console.log(
          `Sermons loaded (${data.cached ? "cached" : "fresh"}):`,
          `Live stream: ${data.liveStream ? "Yes" : "No"}, Total sermons: ${
            data.sermons?.length || 0
          }`
        );
      } else {
        console.warn("Received invalid sermon data:", data);
        throw new Error("Invalid sermon data received");
      }
    } catch (error) {
      console.error("Error fetching sermons:", error);

      // Handle different error types with user-friendly messages
      let errorMessage = "An unexpected error occurred. Please try again.";

      if (error instanceof DOMException) {
        if (error.name === "AbortError") {
          errorMessage = "Request timed out. Please try again.";
        } else {
          errorMessage = `Browser error: ${error.message}`;
        }
      } else if (error instanceof Error) {
        // Check for network-related errors
        if (
          error.message.includes("fetch") ||
          error.message.includes("network") ||
          error.message.includes("timeout") ||
          error.message.includes("aborted")
        ) {
          errorMessage =
            "Network error. Please check your connection and try again.";
        } else {
          errorMessage = error.message;
        }
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Function to manually refresh sermons
  const refreshSermons = () => {
    fetchSermons(true);
  };

  useEffect(() => {
    // Flag to track if the component is mounted
    let isMounted = true;

    // Modified fetch function that checks if component is still mounted
    const safeFetch = async (refresh = false) => {
      if (!isMounted) return;
      try {
        await fetchSermons(refresh);
      } catch (e) {
        console.error("Fetch error:", e);
        // Only update error state if component is still mounted
        if (isMounted) {
          setError(e instanceof Error ? e.message : String(e));
        }
      }
    };

    // Initial fetch
    safeFetch();

    // Poll for live streams every 5 minutes with a more resilient approach
    // Reduced frequency to avoid hitting API quota limits
    const interval = setInterval(
      () => {
        // Only poll if the component is still mounted
        if (isMounted) {
          safeFetch();
        }
      },
      5 * 60 * 1000
    ); // 5 minutes

    // Cleanup function
    return () => {
      isMounted = false;
      clearInterval(interval);
    };
  }, []);

  const handleShare = async (video: YouTubeVideo) => {
    const shareData = {
      title: video.title,
      text: `Watch "${video.title}" by ${video.channelTitle}`,
      url: `https://www.youtube.com/watch?v=${video.id}`,
    };

    try {
      await navigator.share(shareData);
    } catch {
      // If Web Share API is not supported, copy to clipboard as fallback
      navigator.clipboard.writeText(shareData.url);
      alert("Link copied to clipboard!");
    }
  };

  if (loading) {
    return (
      <section className="py-20 bg-secondary/30">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-4 h-4 rounded-full bg-primary animate-pulse"></div>
            <div className="w-4 h-4 rounded-full bg-primary animate-pulse delay-150"></div>
            <div className="w-4 h-4 rounded-full bg-primary animate-pulse delay-300"></div>
          </div>
          <p className="mt-4 text-lg">Loading sermons...</p>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-20 bg-secondary/30">
        <div className="container mx-auto px-4 text-center">
          <div className="bg-destructive/10 text-destructive p-6 rounded-lg max-w-lg mx-auto">
            <p className="font-semibold text-lg mb-2">Error loading sermons</p>
            <p className="mb-4">{error}</p>
            <Button
              onClick={() => fetchSermons(true)}
              variant="outline"
              className="mt-2"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="sermons-section" className="py-20 bg-secondary/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h2 className="text-4xl font-bold mb-4">Latest Sermons</h2>
          <p className="text-lg text-muted-foreground mb-4">
            Be inspired by the Word of God
          </p>

          <div className="flex items-center justify-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshSermons}
              disabled={isRefreshing}
              className="flex items-center gap-1"
            >
              <RefreshCw
                className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
              />
              {isRefreshing ? "Refreshing..." : "Refresh"}
            </Button>

            {lastUpdated && (
              <p className="text-xs text-muted-foreground">
                Last updated: {new Date(lastUpdated).toLocaleTimeString()}
                {isCached && " (cached)"}
              </p>
            )}
          </div>
        </div>

        {liveStream ? (
          <div className="mb-12">
            <div className="bg-background rounded-xl overflow-hidden shadow-lg border-2 border-red-500">
              <div className="bg-red-500 text-white py-2 px-4 flex items-center justify-center">
                <Dot className="animate-pulse h-6 w-6" />
                <span className="font-bold">LIVE NOW - JOIN US!</span>
              </div>
              <div
                className="relative aspect-video cursor-pointer"
                onClick={() => setSelectedSermon(liveStream)}
              >
                <Image
                  src={liveStream.thumbnail}
                  alt={liveStream.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 1200px) 100vw, 1200px"
                />
                <div className="absolute inset-0 bg-black/30 flex items-center justify-center hover:bg-black/40 transition-colors">
                  <Button
                    variant="secondary"
                    size="lg"
                    className="rounded-full"
                  >
                    <Play className="h-8 w-8" />
                  </Button>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-semibold mb-2">
                  {liveStream.title}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {liveStream.channelTitle}
                </p>
                <div className="flex gap-2">
                  <Button
                    className="flex-1"
                    onClick={() => setSelectedSermon(liveStream)}
                  >
                    Watch Now
                  </Button>
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={() =>
                      window.open(
                        `https://www.youtube.com/watch?v=${liveStream.id}`,
                        "_blank"
                      )
                    }
                  >
                    Open on YouTube
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="mb-12 bg-background/80 rounded-xl p-6 text-center">
            <p className="text-lg mb-4">No live sermons currently streaming.</p>
            <p className="text-muted-foreground">
              Check back during our service times or watch our past sermons
              below.
            </p>
          </div>
        )}

        {sermons.length === 0 ? (
          <div className="bg-background/80 rounded-xl p-6 text-center">
            <p className="text-lg mb-4">No sermons available at the moment.</p>
            <p className="text-muted-foreground mb-4">
              Please check back later or visit our YouTube channel directly.
            </p>
            <Button
              variant="outline"
              onClick={() =>
                window.open(
                  `https://www.youtube.com/channel/UCjYkOcueiCATTTJ5FoaxylA`,
                  "_blank"
                )
              }
            >
              Visit YouTube Channel
            </Button>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {currentSermons.map((sermon) => (
                <div
                  key={sermon.id}
                  className="bg-background rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
                >
                  <div
                    className="relative h-48 group cursor-pointer"
                    onClick={() => setSelectedSermon(sermon)}
                  >
                    <Image
                      src={sermon.thumbnail}
                      alt={sermon.title}
                      fill
                      className="object-cover group-hover:opacity-90 transition-opacity"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw"
                    />
                    {sermon.wasLive && (
                      <div className="absolute top-4 left-4 flex items-center bg-gray-800/80 text-white px-3 py-1 rounded-full text-sm">
                        <Play className="h-3 w-3 mr-1" />
                        Past Live
                      </div>
                    )}
                    <div className="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        variant="secondary"
                        size="icon"
                        className="rounded-full"
                      >
                        <Play className="h-6 w-6" />
                      </Button>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-semibold mb-2">
                      {sermon.title}
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      {sermon.channelTitle}
                    </p>
                    <div className="text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(sermon.publishedAt), {
                        addSuffix: true,
                      })}
                    </div>
                    <div className="flex gap-2 mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        onClick={() =>
                          window.open(
                            `https://www.youtube.com/watch?v=${sermon.id}`,
                            "_blank"
                          )
                        }
                      >
                        Watch on YouTube
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        onClick={() => handleShare(sermon)}
                      >
                        <Share2 className="h-4 w-4 mr-2" />
                        Share
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="mt-12 flex justify-center items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={prevPage}
                  disabled={currentPage === 1}
                  className="rounded-full"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                <div className="flex gap-1">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                    (number) => (
                      <Button
                        key={number}
                        variant={currentPage === number ? "default" : "outline"}
                        size="sm"
                        onClick={() => paginate(number)}
                        className={`w-8 h-8 p-0 ${
                          currentPage === number
                            ? "bg-primary text-primary-foreground"
                            : ""
                        }`}
                      >
                        {number}
                      </Button>
                    )
                  )}
                </div>

                <Button
                  variant="outline"
                  size="icon"
                  onClick={nextPage}
                  disabled={currentPage === totalPages}
                  className="rounded-full"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}
          </>
        )}

        {selectedSermon && (
          <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
            <div className="relative w-full max-w-4xl mx-4">
              <Button
                variant="ghost"
                size="icon"
                className="absolute -top-12 right-0 text-white"
                onClick={() => setSelectedSermon(null)}
              >
                <X className="h-6 w-6" />
              </Button>
              <div className="relative pt-[56.25%] w-full">
                <iframe
                  className="absolute inset-0 w-full h-full rounded-lg"
                  src={generateChurchVideoEmbedUrl(selectedSermon.id, true)}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                  title={`${selectedSermon.title} - Times and Seasons Church`}
                ></iframe>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
