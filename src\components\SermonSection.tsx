"use client";

import React, { useState, useEffect, useMemo } from "react";
import {
  Play,
  Share2,
  X,
  Dot,
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  AlertTriangle,
  Rss,
  Wifi,
  WifiOff,
  Activity,
  TrendingUp,
  TrendingDown,
  Minus,
  Users,
  Clock,
  Search,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useRealtimeLiveStream } from "@/hooks/useRealtimeLiveStream";
import { Badge } from "@/components/ui/badge";
import SermonSearch from "@/components/SermonSearch";
import { generateChurchVideoEmbedUrl } from "@/lib/youtube-embed";
// Define our own interface to match the new API
interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  publishedAt: string;
  channelTitle: string;
  isLive?: boolean;
  isPastLive?: boolean;
  liveBroadcastContent?: "live" | "upcoming" | "none";
  wasLive?: boolean;
  verifiedLive?: boolean;
  concurrentViewers?: number;
  status?: string;
}
import { formatDistanceToNow } from "date-fns";

// Extended AbortController interface with timeoutId property
interface ExtendedAbortController extends AbortController {
  timeoutId?: NodeJS.Timeout;
}

export function SermonSection() {
  const [sermons, setSermons] = useState<YouTubeVideo[]>([]);
  const [liveStream, setLiveStream] = useState<YouTubeVideo | null>(null);
  const [recentlyEndedStream, setRecentlyEndedStream] =
    useState<YouTubeVideo | null>(null);
  const [placeholderThumbnail, setPlaceholderThumbnail] = useState<
    string | null
  >(null);
  const [selectedSermon, setSelectedSermon] = useState<YouTubeVideo | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);
  const [isCached, setIsCached] = useState(true);
  const [dataSource, setDataSource] = useState<"rss" | "api" | "api-cached">(
    "api"
  );

  // State for archived streams and notifications
  const [archivedStream, setArchivedStream] = useState<any>(null);
  const [showArchiveNotification, setShowArchiveNotification] = useState(false);
  const [showReplayReadyNotification, setShowReplayReadyNotification] =
    useState(false);

  // Real-time live stream monitoring
  const realtimeStream = useRealtimeLiveStream({
    autoConnect: true,
    onStreamStarted: (stream) => {
      console.log("🔴 Real-time: Stream started!", stream.title);
      setLiveStream(stream);
      // Hide any archive notifications when new stream starts
      setShowArchiveNotification(false);
      setShowReplayReadyNotification(false);
    },
    onStreamEnded: (stream) => {
      console.log("⚫ Real-time: Stream ended!", stream?.title);
      setLiveStream(null);
      setRecentlyEndedStream(stream);
      // Show archive processing notification
      setShowArchiveNotification(true);
      // Refresh sermons to get the recorded version
      fetchSermons(true);
    },
    onViewerCountChanged: (newCount, oldCount) => {
      console.log(
        `👥 Real-time: Viewer count changed: ${oldCount} → ${newCount}`
      );
      // Update live stream with new viewer count
      if (liveStream) {
        setLiveStream((prev) =>
          prev ? { ...prev, concurrentViewers: newCount } : null
        );
      }
    },
  });

  // Listen for archive events
  useEffect(() => {
    const handleArchiveEvents = (event: any) => {
      const data = JSON.parse(event.data);

      switch (data.type) {
        case "stream_archived":
          if (data.data.archivedStream) {
            console.log("📼 Stream archived:", data.data.archivedStream.title);
            setArchivedStream(data.data.archivedStream);

            if (data.data.archivedStream.replayAvailable) {
              setShowReplayReadyNotification(true);
              setShowArchiveNotification(false);
            }
          }
          break;

        case "archive_ready":
          if (data.data.archivedStream) {
            console.log("🎬 Replay ready:", data.data.archivedStream.title);
            setArchivedStream(data.data.archivedStream);
            setShowReplayReadyNotification(true);
            setShowArchiveNotification(false);
            // Refresh sermons to show the replay
            fetchSermons(true);
          }
          break;
      }
    };

    // Connect to SSE for archive events
    const eventSource = new EventSource("/api/live-stream-events");
    eventSource.addEventListener("message", handleArchiveEvents);

    return () => {
      eventSource.close();
    };
  }, [fetchSermons]);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const sermonsPerPage = 9; // 3 rows of 3 sermons each
  const [totalPages, setTotalPages] = useState(1);
  const [pastSermons, setPastSermons] = useState<YouTubeVideo[]>([]);
  const [loadingPastSermons, setLoadingPastSermons] = useState(false);

  // Search state
  const [showSearch, setShowSearch] = useState(false);
  const [searchResults, setSearchResults] = useState<YouTubeVideo[]>([]);
  const [isSearchActive, setIsSearchActive] = useState(false);

  // Current sermons to display - search results, past sermons, or live API
  const currentSermons = useMemo(() => {
    let filteredSermons: YouTubeVideo[];

    if (isSearchActive && searchResults.length > 0) {
      // Show search results
      filteredSermons = searchResults;
    } else if (pastSermons.length > 0) {
      // Show past sermons from pagination
      filteredSermons = pastSermons;
    } else {
      // Show recent sermons from live API
      filteredSermons = sermons.slice(0, sermonsPerPage);
    }

    // Filter out any deleted or private videos
    return filteredSermons.filter((sermon) => {
      // Skip if the video is marked as deleted or private
      if (sermon.status === "deleted" || sermon.status === "private") {
        console.log(`Filtering out deleted/private video: ${sermon.title}`);
        return false;
      }
      return true;
    });
  }, [sermons, pastSermons, searchResults, isSearchActive, sermonsPerPage]);

  // Function to fetch past sermons
  const fetchPastSermons = async (page: number) => {
    try {
      setLoadingPastSermons(true);

      // Build URL with parameters
      let url = `/api/past-sermons?page=${page}&limit=${sermonsPerPage}`;

      // Always use RSS feed (API quota is exceeded)
      // No need to add any parameters

      // Add timestamp to bust cache
      url += `&t=${Date.now()}`;

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch past sermons: ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data.videos)) {
        setPastSermons(data.videos);
        setTotalPages(data.totalPages || 1);
        console.log(
          `Loaded ${data.videos.length} past sermons (page ${page}/${data.totalPages})`
        );
      } else {
        console.warn("Received invalid past sermon data:", data);
        throw new Error("Invalid past sermon data received");
      }
    } catch (error) {
      console.error("Error fetching past sermons:", error);
      // If we fail to fetch past sermons, fall back to the sermons we already have
      setTotalPages(Math.ceil(sermons.length / sermonsPerPage));
    } finally {
      setLoadingPastSermons(false);
    }
  };

  // Change page
  const paginate = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    // Fetch past sermons for this page
    fetchPastSermons(pageNumber);
    // Scroll to top of sermon section
    window.scrollTo({
      top: document.getElementById("sermons-section")?.offsetTop || 0,
      behavior: "smooth",
    });
  };

  // Go to next page
  const nextPage = () => {
    if (currentPage < totalPages) {
      paginate(currentPage + 1);
    }
  };

  // Go to previous page
  const prevPage = () => {
    if (currentPage > 1) {
      paginate(currentPage - 1);
    }
  };

  // Function to fetch sermons with optional refresh
  const fetchSermons = async (refresh = false) => {
    try {
      setError(null);
      if (refresh) {
        setIsRefreshing(true);
      }

      // Use a timeout to prevent hanging requests
      const fetchWithTimeout = async (url: string, timeoutMs = 30000) => {
        // Increased to 30 seconds
        // Create a controller for this specific request
        const controller = new AbortController() as ExtendedAbortController;

        // Create a promise that resolves after the timeout
        const timeoutPromise = new Promise((_, reject) => {
          const timeoutId = setTimeout(() => {
            controller.abort();
            reject(new Error("Request timed out"));
          }, timeoutMs);

          // Store the timeout ID on the controller for cleanup
          controller.timeoutId = timeoutId;
        });

        try {
          // Race between the fetch and the timeout
          const response = (await Promise.race([
            fetch(url, { signal: controller.signal }),
            timeoutPromise,
          ])) as Response;

          // Clear the timeout if fetch wins the race
          if (controller.timeoutId) {
            clearTimeout(controller.timeoutId);
            controller.timeoutId = undefined;
          }

          return response;
        } catch (error) {
          // Clear the timeout if it hasn't fired yet
          if (controller.timeoutId) {
            clearTimeout(controller.timeoutId);
            controller.timeoutId = undefined;
          }

          // Rethrow with a more specific message for AbortError
          if (error instanceof DOMException && error.name === "AbortError") {
            throw new Error(
              "Request timed out. The server might be busy processing YouTube API requests. Please try again later."
            );
          }
          throw error;
        }
      };

      // Use the improved live status API endpoint
      let url = "/api/live-status";
      const params = new URLSearchParams();

      // Always add debug parameter for better troubleshooting
      params.append("debug", "true");

      // Always add a timestamp to bust any caching
      params.append("t", Date.now().toString());

      // Always use RSS feed
      params.append("rss", "true");

      // Add parameters to URL if any exist
      const queryString = params.toString();
      if (queryString) {
        url += `?${queryString}`;
      }

      // Try to fetch with a timeout and retry mechanism
      let response: Response | undefined;
      let retries = 0;
      const maxRetries = 2;

      while (retries <= maxRetries) {
        try {
          response = await fetchWithTimeout(url);
          break; // If successful, break out of the retry loop
        } catch (error) {
          retries++;
          console.log(`Attempt ${retries} of ${maxRetries + 1} failed`);

          if (retries > maxRetries) {
            // If we've exhausted all retries, rethrow the error
            throw error;
          }

          // Wait before retrying (exponential backoff)
          const waitTime = 1000 * Math.pow(2, retries);
          console.log(`Waiting ${waitTime}ms before retrying...`);
          await new Promise((resolve) => setTimeout(resolve, waitTime));
        }
      }

      // Check if response is defined
      if (!response) {
        throw new Error(
          "Failed to fetch sermons: No response received after retries"
        );
      }

      if (!response.ok) {
        console.error(`Failed to fetch sermons: ${response.status}`, response);
        throw new Error(`Failed to fetch sermons: ${response.status}`);
      }

      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error("Error parsing JSON response:", parseError);
        throw new Error("Failed to parse API response. Please try again.");
      }

      // Handle error responses but still use any data provided
      if (data.error) {
        console.error("API returned an error:", data.error);
        if (data.errorDetails) {
          console.error("Error details:", data.errorDetails);
        }

        // If the API returned sermons despite the error, we can still use them
        if (Array.isArray(data.sermons)) {
          console.log("API returned sermons despite error, using them");
          // Continue processing but still set the error
          setError(data.error);
        } else {
          // No usable data, throw error
          throw new Error(data.error);
        }
      }

      // Only update state if we have valid data
      if (Array.isArray(data.sermons)) {
        setSermons(data.sermons);
        setLiveStream(data.isLive ? data.liveStream : null);
        setRecentlyEndedStream(data.recentPastLiveStream || null);
        setIsCached(data.cached || false);
        setDataSource(data.dataSource || "api");
        setLastUpdated(data.timestamp);

        // Set placeholder thumbnail from the first sermon if available
        if (
          data.sermons &&
          data.sermons.length > 0 &&
          data.sermons[0].thumbnail
        ) {
          setPlaceholderThumbnail(data.sermons[0].thumbnail);
        }

        // Log live status for debugging
        console.log(`Live status: ${data.isLive ? "LIVE" : "NOT LIVE"}`);
        if (data.isLive && data.liveStream) {
          console.log(
            `Live stream: "${data.liveStream.title}" with ${data.liveStream.concurrentViewers || 0} viewers`
          );
        }

        // Log debug information if available
        if (data.debug) {
          console.log("Debug information:", data.debug);
        }

        // Reset to first page when new sermons are loaded
        setCurrentPage(1);

        // Log data source information
        console.log(
          `Sermons loaded (${data.dataSource || "unknown"}${data.cached ? " - cached" : ""}):`,
          `Live stream: ${data.liveStream ? "Yes" : "No"}, ` +
            `Recent stream: ${data.recentPastLiveStream ? "Yes" : "No"}, ` +
            `Total sermons: ${data.sermons?.length || 0}`
        );
      } else {
        console.warn("Received invalid sermon data:", data);
        throw new Error("Invalid sermon data received");
      }
    } catch (error) {
      console.error("Error fetching sermons:", error);

      // Handle different error types with user-friendly messages
      let errorMessage = "An unexpected error occurred. Please try again.";
      let isQuotaError = false;

      if (error instanceof DOMException) {
        if (error.name === "AbortError") {
          errorMessage = "Request timed out. Please try again.";
        } else {
          errorMessage = `Browser error: ${error.message}`;
        }
      } else if (error instanceof Error) {
        // Check for quota errors first
        if (error.message.includes("quota") || error.message.includes("429")) {
          errorMessage =
            "YouTube API quota exceeded. Using cached data if available.";
          isQuotaError = true;
        }
        // Check for network-related errors
        else if (
          error.message.includes("fetch") ||
          error.message.includes("network") ||
          error.message.includes("timeout") ||
          error.message.includes("aborted")
        ) {
          errorMessage =
            "Network error. Please check your connection and try again.";
        } else {
          errorMessage = error.message;
        }
      }

      setError(errorMessage);

      // Clear error after a delay
      setTimeout(
        () => {
          setError(null);
        },
        isQuotaError ? 10000 : 5000
      ); // 10 seconds for quota errors, 5 seconds for others
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Function to manually refresh sermons
  const refreshSermons = () => {
    fetchSermons(true);
    // Also refresh past sermons for the current page
    fetchPastSermons(currentPage);
  };

  // Handle search results
  const handleSearchResults = (results: YouTubeVideo[]) => {
    setSearchResults(results);
    setIsSearchActive(results.length > 0);
    console.log(`🔍 Search results updated: ${results.length} sermons`);
  };

  // Handle search clear
  const handleSearchClear = () => {
    setSearchResults([]);
    setIsSearchActive(false);
    setShowSearch(false);
    console.log("🔍 Search cleared");
  };

  useEffect(() => {
    // Flag to track if the component is mounted
    let isMounted = true;

    // Modified fetch function that checks if component is still mounted
    const safeFetch = async (refresh = false) => {
      if (!isMounted) return;
      try {
        await fetchSermons(refresh);
      } catch (e) {
        console.error("Fetch error:", e);
        // Only update error state if component is still mounted
        if (isMounted) {
          setError(e instanceof Error ? e.message : String(e));
        }
      }
    };

    // Initial fetch of live status - force refresh to get the latest data
    safeFetch(true);

    // Initial fetch of past sermons
    fetchPastSermons(1);

    // Set up polling for live streams
    // Poll more frequently when we're live to quickly detect when the stream ends
    const pollingInterval = setInterval(
      () => {
        // Only poll if the component is still mounted
        if (isMounted) {
          // Force refresh if we're live to ensure we detect when the stream ends
          const isCurrentlyLive = !!liveStream;
          safeFetch(isCurrentlyLive);

          // Log polling
          console.log(
            `Polling for live status (${isCurrentlyLive ? "LIVE" : "not live"})`
          );
        }
      },
      10 * 1000 // Poll every 10 seconds to quickly detect live status changes
    );

    // Cleanup function
    return () => {
      isMounted = false;
      clearInterval(pollingInterval);
    };
    // We need to include fetchSermons and fetchPastSermons in the dependency array
    // But we don't want to recreate them on every render, so we'll disable the eslint rule
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleShare = async (video: YouTubeVideo) => {
    const shareData = {
      title: video.title,
      text: `Watch "${video.title}" by ${video.channelTitle}`,
      url: `https://www.youtube.com/watch?v=${video.id}`,
    };

    try {
      await navigator.share(shareData);
    } catch {
      // If Web Share API is not supported, copy to clipboard as fallback
      navigator.clipboard.writeText(shareData.url);
      alert("Link copied to clipboard!");
    }
  };

  // Real-time status indicator component
  const RealtimeStatusIndicator = () => {
    const getConnectionIcon = () => {
      switch (realtimeStream.connectionStatus) {
        case "connected":
          return <Wifi className="h-4 w-4 text-green-500" />;
        case "connecting":
          return <Activity className="h-4 w-4 text-yellow-500 animate-pulse" />;
        case "disconnected":
        case "error":
          return <WifiOff className="h-4 w-4 text-red-500" />;
        default:
          return <WifiOff className="h-4 w-4 text-gray-500" />;
      }
    };

    const getViewerTrendIcon = () => {
      if (!realtimeStream.healthMetrics) return null;

      switch (realtimeStream.healthMetrics.viewerTrend) {
        case "increasing":
          return <TrendingUp className="h-3 w-3 text-green-500" />;
        case "decreasing":
          return <TrendingDown className="h-3 w-3 text-red-500" />;
        case "stable":
          return <Minus className="h-3 w-3 text-blue-500" />;
        default:
          return null;
      }
    };

    return (
      <div className="flex items-center gap-2 text-xs text-muted-foreground">
        {getConnectionIcon()}
        <span className="capitalize">{realtimeStream.connectionStatus}</span>

        {realtimeStream.isLive && (
          <>
            <Dot className="h-3 w-3" />
            <Users className="h-3 w-3" />
            <span>{realtimeStream.viewerCount.toLocaleString()}</span>
            {getViewerTrendIcon()}

            {realtimeStream.healthMetrics && (
              <>
                <Dot className="h-3 w-3" />
                <Badge
                  variant={
                    realtimeStream.healthMetrics.healthScore >= 80
                      ? "default"
                      : realtimeStream.healthMetrics.healthScore >= 60
                        ? "secondary"
                        : realtimeStream.healthMetrics.healthScore >= 40
                          ? "outline"
                          : "destructive"
                  }
                  className="text-xs px-1 py-0"
                >
                  {realtimeStream.healthMetrics.healthScore}%
                </Badge>
              </>
            )}
          </>
        )}

        {realtimeStream.lastUpdate && (
          <>
            <Dot className="h-3 w-3" />
            <Clock className="h-3 w-3" />
            <span>
              {new Date(realtimeStream.lastUpdate).toLocaleTimeString()}
            </span>
          </>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <section className="py-20 bg-secondary/30">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-4 h-4 rounded-full bg-primary animate-pulse"></div>
            <div className="w-4 h-4 rounded-full bg-primary animate-pulse delay-150"></div>
            <div className="w-4 h-4 rounded-full bg-primary animate-pulse delay-300"></div>
          </div>
          <p className="mt-4 text-lg">Loading sermons...</p>
        </div>
      </section>
    );
  }

  if (error) {
    // If there's an error but we still have sermons, show a warning banner instead of full error page
    if (sermons.length > 0) {
      return (
        <section id="sermons-section" className="py-20 bg-secondary/30">
          <div className="container mx-auto px-4">
            <div className="bg-amber-500/10 text-amber-700 dark:text-amber-400 p-4 rounded-lg mb-8">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-semibold">Warning: {error}</p>
                  <p className="text-sm mt-1">
                    Showing cached or partial data. Some information may be
                    outdated.
                  </p>
                  <div className="flex gap-2 mt-2">
                    <Button
                      onClick={() => fetchSermons(true)}
                      variant="outline"
                      size="sm"
                      className="bg-amber-500/20"
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Refresh
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Continue with the normal sermon section content */}
            <div className="text-center mb-8">
              <h2 className="text-4xl font-bold mb-4">Latest Sermons</h2>
              <p className="text-lg text-muted-foreground mb-4">
                Be inspired by the Word of God
              </p>

              {/* Rest of the content will be included in the regular return */}
              {/* ... */}
            </div>

            {/* Display sermons as normal */}
            {/* ... */}
          </div>
        </section>
      );
    }
    // If there's an error and no sermons, show full error page
    else {
      return (
        <section className="py-20 bg-secondary/30">
          <div className="container mx-auto px-4 text-center">
            <div className="bg-destructive/10 text-destructive p-6 rounded-lg max-w-lg mx-auto">
              <p className="font-semibold text-lg mb-2">
                Error loading sermons
              </p>
              <p className="mb-4">{error}</p>
              <div className="flex gap-2 justify-center">
                <Button
                  onClick={() => fetchSermons(true)}
                  variant="outline"
                  className="mt-2"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        </section>
      );
    }
  }

  return (
    <section id="sermons-section" className="py-20 bg-secondary/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h2 className="text-4xl font-bold mb-4">Latest Sermons</h2>
          <p className="text-lg text-muted-foreground mb-4">
            Be inspired by the Word of God
          </p>

          <div className="flex flex-col items-center gap-4">
            {/* Real-time status indicator */}
            <div className="bg-background/50 rounded-lg p-3 border">
              <div className="flex items-center justify-center gap-2 mb-2">
                <span className="text-sm font-medium">
                  Real-time Monitoring:
                </span>
                <RealtimeStatusIndicator />
              </div>
              {realtimeStream.error && (
                <div className="text-xs text-red-500 text-center">
                  {realtimeStream.error}
                </div>
              )}
            </div>

            <div className="flex flex-col items-center gap-4">
              {/* Search Toggle and Controls */}
              <div className="flex items-center justify-center gap-2">
                <Button
                  variant={showSearch ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowSearch(!showSearch)}
                  className="flex items-center gap-1"
                >
                  <Search className="h-4 w-4" />
                  {showSearch ? "Hide Search" : "Search Sermons"}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshSermons}
                  disabled={isRefreshing}
                  className="flex items-center gap-1"
                >
                  <RefreshCw
                    className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
                  />
                  {isRefreshing ? "Refreshing..." : "Refresh"}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={realtimeStream.reconnect}
                  disabled={realtimeStream.connectionStatus === "connecting"}
                  className="flex items-center gap-1"
                >
                  <Activity className="h-4 w-4" />
                  Reconnect Real-time
                </Button>

                <div className="flex flex-col items-center">
                  <Button
                    variant="default"
                    size="sm"
                    className="bg-green-600 hover:bg-green-700 text-white"
                    disabled
                  >
                    <Rss className="h-4 w-4 mr-1" />
                    Using RSS Feed (No Quota Limits)
                  </Button>
                  <span className="text-xs text-muted-foreground mt-1">
                    YouTube API quota exceeded
                  </span>
                </div>
              </div>

              {/* Search Results Status */}
              {isSearchActive && (
                <div className="text-center">
                  <Badge variant="secondary" className="gap-1">
                    <Search className="h-3 w-3" />
                    Showing {searchResults.length} search results
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSearchClear}
                      className="h-4 w-4 p-0 ml-1"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                </div>
              )}
            </div>
          </div>

          {lastUpdated && (
            <p className="text-xs text-muted-foreground">
              Last updated: {new Date(lastUpdated).toLocaleTimeString()}
              {dataSource === "rss" && " (RSS feed)"}
              {dataSource === "api-cached" && " (API cached)"}
              {dataSource === "api" && !isCached && " (API)"}
              {pastSermons.length > 0 &&
                ` • ${pastSermons.length} sermons loaded`}
            </p>
          )}
        </div>

        {/* Search Component */}
        {showSearch && (
          <div className="mb-8">
            <SermonSearch
              onResultsChange={handleSearchResults}
              className="max-w-4xl mx-auto"
            />
          </div>
        )}

        {/* Archive Processing Notification */}
        {showArchiveNotification && recentlyEndedStream && (
          <div className="mb-8">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <div className="flex-1">
                  <h3 className="font-semibold text-blue-900">
                    📼 Saving Live Stream
                  </h3>
                  <p className="text-blue-700 text-sm">
                    "{recentlyEndedStream.title}" is being processed and will be
                    available for replay soon.
                  </p>
                </div>
                <button
                  onClick={() => setShowArchiveNotification(false)}
                  className="ml-auto text-blue-500 hover:text-blue-700 p-1"
                >
                  ✕
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Replay Ready Notification */}
        {showReplayReadyNotification && archivedStream && (
          <div className="mb-8">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">✓</span>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-green-900">
                    🎬 Replay Available
                  </h3>
                  <p className="text-green-700 text-sm">
                    "{archivedStream.title}" is now available for replay below.
                  </p>
                </div>
                <button
                  onClick={() => setShowReplayReadyNotification(false)}
                  className="ml-auto text-green-500 hover:text-green-700 p-1"
                >
                  ✕
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Show live stream UI if it exists (it will only be set if truly live) */}
        {liveStream ? (
          <div className="mb-12">
            <div className="bg-background rounded-xl overflow-hidden shadow-lg border-4 border-red-600 relative">
              {/* Pulsing live indicator */}
              <div className="absolute -top-3 -right-3 z-10">
                <div className="relative">
                  <div className="absolute inset-0 rounded-full bg-red-600 animate-ping opacity-75"></div>
                  <div className="relative rounded-full bg-red-600 text-white px-3 py-1 text-xs font-bold">
                    LIVE
                  </div>
                </div>
              </div>
              <div className="bg-red-500 text-white py-2 px-4 flex items-center justify-between">
                <div className="flex items-center">
                  <Dot className="animate-pulse h-6 w-6" />
                  <span className="font-bold">
                    STREAMING LIVE NOW - JOIN US!
                  </span>
                  <span className="ml-2 bg-white text-red-500 text-xs px-1.5 py-0.5 rounded-full font-semibold">
                    LIVE
                  </span>
                  {liveStream.concurrentViewers && (
                    <span className="ml-2 bg-white/20 text-white text-xs px-1.5 py-0.5 rounded-full">
                      {liveStream.concurrentViewers} watching
                    </span>
                  )}
                </div>
                <div className="text-sm">
                  {liveStream.publishedAt && (
                    <span>
                      {(() => {
                        const publishTime = new Date(
                          liveStream.publishedAt
                        ).getTime();
                        const oneHourAgo = Date.now() - 60 * 60 * 1000;

                        // If it started less than an hour ago, show "Started X minutes ago"
                        if (publishTime > oneHourAgo) {
                          return `Started ${formatDistanceToNow(
                            new Date(liveStream.publishedAt),
                            {
                              addSuffix: true,
                            }
                          )}`;
                        } else {
                          // For older streams, show the actual start time
                          return `Started at ${new Date(liveStream.publishedAt).toLocaleTimeString()}`;
                        }
                      })()}
                    </span>
                  )}
                </div>
              </div>
              <div
                className="relative aspect-video cursor-pointer"
                onClick={() => setSelectedSermon(liveStream)}
              >
                <Image
                  src={liveStream.thumbnail}
                  alt={liveStream.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 1200px) 100vw, 1200px"
                />
                <div className="absolute inset-0 bg-black/30 flex items-center justify-center hover:bg-black/40 transition-colors">
                  <Button
                    variant="secondary"
                    size="lg"
                    className="rounded-full"
                  >
                    <Play className="h-8 w-8" />
                  </Button>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-semibold mb-2">
                  {liveStream.title}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {liveStream.channelTitle}
                </p>
                <div className="flex gap-2">
                  <Button
                    className="flex-1"
                    onClick={() => setSelectedSermon(liveStream)}
                  >
                    Watch Now
                  </Button>
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={() =>
                      window.open(
                        `https://www.youtube.com/watch?v=${liveStream.id}`,
                        "_blank"
                      )
                    }
                  >
                    Open on YouTube
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ) : recentlyEndedStream ? (
          /* Show recently ended stream UI - this should always be available now */
          <div className="mb-12">
            <div className="bg-background rounded-xl overflow-hidden shadow-lg border-4 border-amber-500 relative">
              {/* Latest service indicator */}
              <div className="absolute -top-3 -right-3 z-10">
                <div className="relative">
                  <div className="relative rounded-full bg-amber-500 text-white px-3 py-1 text-xs font-bold">
                    LATEST
                  </div>
                </div>
              </div>
              <div className="bg-amber-500 text-white py-2 px-4 flex items-center justify-between">
                <div className="flex items-center">
                  <span className="font-bold">LATEST SERVICE - WATCH NOW</span>
                </div>
                <div className="text-sm">
                  {recentlyEndedStream.publishedAt && (
                    <span>
                      {(() => {
                        const publishTime = new Date(
                          recentlyEndedStream.publishedAt
                        ).getTime();
                        const minutesAgo = Math.round(
                          (Date.now() - publishTime) / (60 * 1000)
                        );

                        if (minutesAgo < 60) {
                          return `Published ${minutesAgo} minutes ago`;
                        } else {
                          const hoursAgo = Math.round(minutesAgo / 60);
                          return `Published ${hoursAgo} ${hoursAgo === 1 ? "hour" : "hours"} ago`;
                        }
                      })()}
                    </span>
                  )}
                </div>
              </div>
              <div
                className="relative aspect-video cursor-pointer"
                onClick={() => setSelectedSermon(recentlyEndedStream)}
              >
                <Image
                  src={recentlyEndedStream.thumbnail}
                  alt={recentlyEndedStream.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 1200px) 100vw, 1200px"
                />
                <div className="absolute inset-0 bg-black/30 flex items-center justify-center hover:bg-black/40 transition-colors">
                  <Button
                    variant="secondary"
                    size="lg"
                    className="rounded-full"
                  >
                    <Play className="h-8 w-8" />
                  </Button>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-semibold mb-2">
                  {recentlyEndedStream.title}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {recentlyEndedStream.channelTitle}
                </p>
                <div className="flex gap-2">
                  <Button
                    className="flex-1"
                    onClick={() => setSelectedSermon(recentlyEndedStream)}
                  >
                    Watch Latest Service
                  </Button>
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={() =>
                      window.open(
                        `https://www.youtube.com/watch?v=${recentlyEndedStream.id}`,
                        "_blank"
                      )
                    }
                  >
                    Open on YouTube
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="mb-12">
            <div className="bg-background rounded-xl overflow-hidden shadow-lg">
              <div className="relative aspect-video">
                <Image
                  src={placeholderThumbnail || "/liveplaceholder.jpg"}
                  alt="No live sermons currently streaming"
                  fill
                  className="object-cover"
                  sizes="(max-width: 1200px) 100vw, 1200px"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-b from-black/70 to-black/80 flex flex-col items-center justify-center text-white text-center p-6">
                  <h3 className="text-2xl md:text-3xl font-bold mb-4">
                    No Live Stream Currently
                  </h3>
                  <p className="text-lg max-w-lg mb-4">
                    We&apos;re not streaming live right now. Please check back
                    during our service times or watch our past sermons below.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-2 mt-4">
                    <Button
                      variant="default"
                      size="lg"
                      className="bg-red-600 hover:bg-red-700 text-white font-bold text-lg px-8 py-6 shadow-lg"
                      onClick={(e) => {
                        e.stopPropagation();
                        refreshSermons();
                      }}
                      disabled={isRefreshing}
                    >
                      {isRefreshing ? (
                        <>
                          <div className="mr-3 h-6 w-6 animate-spin rounded-full border-4 border-white border-t-transparent"></div>
                          Checking Live Status...
                        </>
                      ) : (
                        <>
                          <Dot className="h-6 w-6 text-white mr-2" />
                          Check if We&apos;re Live Now
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
              <div className="p-6 text-center">
                <p className="text-muted-foreground">
                  Join us for our next live service or explore our sermon
                  archive.
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() =>
                    window.open(
                      `https://www.youtube.com/channel/UCjYkOcueiCATTTJ5FoaxylA`,
                      "_blank"
                    )
                  }
                >
                  Visit Our YouTube Channel
                </Button>
              </div>
            </div>
          </div>
        )}

        {sermons.length === 0 ? (
          <div className="bg-background/80 rounded-xl p-6 text-center">
            <p className="text-lg mb-4">No sermons available at the moment.</p>
            <p className="text-muted-foreground mb-4">
              {error ? (
                <>
                  The YouTube API quota has been exceeded. This is a limitation
                  set by YouTube that restricts the number of API requests we
                  can make in a day.
                  <br />
                  <br />
                  Please try again later or visit our YouTube channel directly.
                </>
              ) : (
                "Please check back later or visit our YouTube channel directly."
              )}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="outline"
                onClick={() =>
                  window.open(
                    `https://www.youtube.com/channel/UCjYkOcueiCATTTJ5FoaxylA`,
                    "_blank"
                  )
                }
              >
                Visit YouTube Channel
              </Button>
              {error && (
                <Button variant="outline" onClick={() => fetchSermons(true)}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              )}
            </div>
          </div>
        ) : (
          <>
            {loadingPastSermons ? (
              <div className="flex items-center justify-center py-12">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-primary animate-pulse"></div>
                  <div className="w-3 h-3 rounded-full bg-primary animate-pulse delay-150"></div>
                  <div className="w-3 h-3 rounded-full bg-primary animate-pulse delay-300"></div>
                  <span className="ml-2 text-muted-foreground">
                    Loading sermons...
                  </span>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {currentSermons.map((sermon) => (
                  <div
                    key={sermon.id}
                    className="bg-background rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
                  >
                    <div
                      className="relative h-48 group cursor-pointer"
                      onClick={() => setSelectedSermon(sermon)}
                    >
                      <Image
                        src={sermon.thumbnail.replace(
                          "maxresdefault.jpg",
                          "hqdefault.jpg"
                        )}
                        alt={sermon.title}
                        fill
                        className="object-cover group-hover:opacity-90 transition-opacity"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw"
                        onError={(e) => {
                          // If the image fails to load, replace with a fallback
                          const target = e.target as HTMLImageElement;
                          if (target.src.includes("maxresdefault.jpg")) {
                            target.src = target.src.replace(
                              "maxresdefault.jpg",
                              "hqdefault.jpg"
                            );
                          } else if (target.src.includes("hqdefault.jpg")) {
                            target.src = target.src.replace(
                              "hqdefault.jpg",
                              "mqdefault.jpg"
                            );
                          }
                        }}
                      />
                      {(sermon.isPastLive || sermon.wasLive) && (
                        <div className="absolute top-4 left-4 flex items-center bg-amber-600/90 text-white px-3 py-1 rounded-full text-sm">
                          <Play className="h-3 w-3 mr-1" />
                          Past Service
                        </div>
                      )}
                      <div className="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          variant="secondary"
                          size="icon"
                          className="rounded-full"
                        >
                          <Play className="h-6 w-6" />
                        </Button>
                      </div>
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-2">
                        {sermon.title}
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        {sermon.channelTitle}
                      </p>
                      <div className="text-sm text-muted-foreground">
                        {formatDistanceToNow(new Date(sermon.publishedAt), {
                          addSuffix: true,
                        })}
                      </div>
                      <div className="flex gap-2 mt-4">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={() =>
                            window.open(
                              `https://www.youtube.com/watch?v=${sermon.id}`,
                              "_blank"
                            )
                          }
                        >
                          Watch on YouTube
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={() => handleShare(sermon)}
                        >
                          <Share2 className="h-4 w-4 mr-2" />
                          Share
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination Controls - Hide when search is active */}
            {totalPages > 1 && !isSearchActive && (
              <div className="mt-12 flex flex-wrap justify-center items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={prevPage}
                  disabled={currentPage === 1 || loadingPastSermons}
                  className="rounded-full"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                <div className="flex flex-wrap gap-1 justify-center">
                  {/* Show limited page numbers on mobile */}
                  {Array.from({ length: totalPages }, (_, i) => i + 1)
                    .filter((number) => {
                      // On smaller screens, show fewer page numbers
                      const isMobile = window.innerWidth < 640;
                      if (!isMobile) return true;

                      // On mobile, only show current page, first, last, and adjacent pages
                      return (
                        number === 1 ||
                        number === totalPages ||
                        Math.abs(number - currentPage) <= 1
                      );
                    })
                    .map((number, index, filteredArray) => (
                      <React.Fragment key={number}>
                        {/* Add ellipsis if pages are skipped */}
                        {index > 0 &&
                          filteredArray[index - 1] !== number - 1 && (
                            <span className="flex items-center px-2">...</span>
                          )}

                        <Button
                          variant={
                            currentPage === number ? "default" : "outline"
                          }
                          size="sm"
                          onClick={() => paginate(number)}
                          disabled={loadingPastSermons}
                          className={`w-8 h-8 p-0 ${
                            currentPage === number
                              ? "bg-primary text-primary-foreground"
                              : ""
                          }`}
                        >
                          {number}
                        </Button>
                      </React.Fragment>
                    ))}
                </div>

                <Button
                  variant="outline"
                  size="icon"
                  onClick={nextPage}
                  disabled={currentPage === totalPages || loadingPastSermons}
                  className="rounded-full"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>

                {loadingPastSermons && (
                  <div className="ml-2 flex items-center">
                    <div className="w-3 h-3 rounded-full bg-primary animate-pulse"></div>
                  </div>
                )}
              </div>
            )}
          </>
        )}

        {selectedSermon && (
          <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
            <div className="relative w-full max-w-4xl mx-4">
              <Button
                variant="ghost"
                size="icon"
                className="absolute -top-12 right-0 text-white"
                onClick={() => setSelectedSermon(null)}
              >
                <X className="h-6 w-6" />
              </Button>
              <div className="relative pt-[56.25%] w-full">
                <iframe
                  className="absolute inset-0 w-full h-full rounded-lg"
                  src={generateChurchVideoEmbedUrl(selectedSermon.id, true)}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                  title={`${selectedSermon.title} - Times and Seasons Church`}
                ></iframe>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
