/**
 * Direct YouTube live check that doesn't rely on the API or RSS feed
 * This uses a direct fetch to the YouTube channel page to check if it's live
 */

import { YouTubeVideo } from '@/types/youtube';

// YouTube channel URL format
const YOUTUBE_CHANNEL_URL = 'https://www.youtube.com/channel/';

/**
 * Checks if a YouTube channel is currently live by directly scraping the channel page
 * This doesn't use the API, so it doesn't count against quota limits
 */
export async function checkIfChannelIsLive(channelId: string): Promise<{
  isLive: boolean;
  liveStream: YouTubeVideo | null;
}> {
  try {
    console.log(`Direct check if channel ${channelId} is live`);
    
    // Fetch the channel page
    const response = await fetch(`${YOUTUBE_CHANNEL_URL}${channelId}/live`, {
      headers: {
        // Pretend to be a browser to avoid being blocked
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      cache: 'no-store'
    });
    
    if (!response.ok) {
      console.error(`Failed to fetch channel page: ${response.status} ${response.statusText}`);
      return { isLive: false, liveStream: null };
    }
    
    const html = await response.text();
    
    // Check if the channel is live by looking for specific patterns in the HTML
    const isLive = html.includes('{"text":" watching now"}') || 
                  html.includes('"isLiveNow":true') || 
                  html.includes('"status":"LIVE"');
    
    console.log(`Direct check result: Channel is ${isLive ? 'LIVE' : 'NOT LIVE'}`);
    
    if (!isLive) {
      return { isLive: false, liveStream: null };
    }
    
    // Try to extract video ID
    let videoId = '';
    const videoIdMatch = html.match(/(?:"videoId"|"videoRenderer":{"videoId"):"([^"]+)"/);
    if (videoIdMatch && videoIdMatch[1]) {
      videoId = videoIdMatch[1];
    }
    
    // Try to extract title
    let title = 'Live Stream';
    const titleMatch = html.match(/(?:"title":{"runs":\[{"text":"([^"]+)"}\])|(?:"title":{"simpleText":"([^"]+)")/);
    if (titleMatch) {
      title = titleMatch[1] || titleMatch[2] || title;
    }
    
    // Create a live stream object
    const liveStream: YouTubeVideo = {
      id: videoId,
      title: title,
      description: 'Live stream detected via direct check',
      thumbnail: `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`,
      publishedAt: new Date().toISOString(),
      channelTitle: 'Times and Seasons Church',
      isLive: true,
      isPastLive: false,
      liveBroadcastContent: 'live',
    };
    
    return { isLive, liveStream };
  } catch (error) {
    console.error('Error in direct live check:', error);
    return { isLive: false, liveStream: null };
  }
}
