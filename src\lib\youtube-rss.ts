/**
 * YouTube RSS feed parser
 * This module fetches and parses YouTube channel RSS feeds to get video data
 * without using the YouTube API (no quota limits)
 */

import { XMLParser } from "fast-xml-parser";
import { YouTubeVideo } from "@/types/youtube";

// YouTube RSS feed URL format
const YOUTUBE_RSS_URL = "https://www.youtube.com/feeds/videos.xml?channel_id=";

/**
 * Fetches videos from a YouTube channel's RSS feed
 * This doesn't count against YouTube API quota
 */
export async function getVideosFromRSS(channelId: string): Promise<{
  videos: YouTubeVideo[];
  liveStream: YouTubeVideo | null;
  recentPastLiveStream: YouTubeVideo | null;
}> {
  try {
    if (!channelId) {
      throw new Error("Channel ID is required");
    }

    console.log(`Fetching RSS feed for channel ${channelId}`);

    // Fetch the RSS feed
    const response = await fetch(`${YOUTUBE_RSS_URL}${channelId}`, {
      next: { revalidate: 60 }, // Revalidate every minute
    });

    if (!response.ok) {
      throw new Error(
        `Failed to fetch RSS feed: ${response.status} ${response.statusText}`
      );
    }

    const xml = await response.text();

    // Parse the XML
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "@_",
    });

    const result = parser.parse(xml);

    if (
      !result.feed ||
      !result.feed.entry ||
      !Array.isArray(result.feed.entry)
    ) {
      console.warn("Invalid RSS feed format:", result);
      return { videos: [], liveStream: null, recentPastLiveStream: null };
    }

    // Process the entries
    const videos: YouTubeVideo[] = result.feed.entry.map((entry: any) => {
      // Extract video ID from the link
      const videoId = entry.link["@_href"].split("v=")[1];

      // Check if this might be a live stream or past live stream based on title
      const title = entry.title?.toLowerCase() || "";
      const description =
        entry.summary?._cdata?.toLowerCase() ||
        entry.summary?.toLowerCase() ||
        "";

      // More aggressive matching for past live streams
      const isPotentialLiveStream =
        title.includes("live") ||
        title.includes("stream") ||
        title.includes("service") ||
        title.includes("sunday") ||
        title.includes("worship") ||
        title.includes("sermon") ||
        description.includes("live") ||
        description.includes("service") ||
        description.includes("sunday") ||
        description.includes("worship");

      // Calculate how recent the video is
      const publishedAt = entry.published;
      const publishTime = new Date(publishedAt).getTime();
      const minutesAgo = Math.round((Date.now() - publishTime) / (60 * 1000));

      // Consider any video published in the last 3 hours as potentially live
      // This is more aggressive to ensure we don't miss live streams
      const isVeryRecent = minutesAgo < 180; // Less than 3 hours old

      // Determine if this is likely a live stream
      // Note: RSS doesn't provide actual live status, so this is a best guess
      // We're being more aggressive here to ensure we catch live streams
      const isLikelyLive =
        (isPotentialLiveStream && isVeryRecent) ||
        (title.includes("live") && isVeryRecent);

      // For RSS, we'll mark videos differently based on content
      // Service-related videos get marked as past live streams
      const isPastLive = isPotentialLiveStream;

      // Get a reliable thumbnail URL (hqdefault always exists)
      const thumbnailUrl = `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`;

      return {
        id: videoId,
        title: entry.title || "",
        description: entry.summary?._cdata || entry.summary || "",
        thumbnail: thumbnailUrl,
        publishedAt: publishedAt,
        channelTitle: result.feed.title || entry.author?.name || "",
        isLive: isLikelyLive,
        isPastLive: isPastLive,
        // RSS doesn't provide these, but we include them for compatibility
        liveBroadcastContent: isLikelyLive ? "live" : "none",
        wasLive: isPastLive,
      };
    });

    // Sort by published date (newest first)
    videos.sort((a, b) => {
      // Parse dates properly
      const dateA = new Date(a.publishedAt).getTime();
      const dateB = new Date(b.publishedAt).getTime();

      // Log the comparison for debugging
      console.log(
        `Comparing dates: "${a.title}" (${a.publishedAt}) vs "${b.title}" (${b.publishedAt})`
      );
      console.log(`  Parsed timestamps: ${dateA} vs ${dateB}`);

      return dateB - dateA;
    });

    // Find potential live stream (very recent video that might be live)
    const potentialLiveStream = videos.find((video) => video.isLive) || null;

    // Log detailed information about potential live streams
    if (potentialLiveStream) {
      console.log(
        `RSS: Found potential live stream: "${potentialLiveStream.title}"`,
        {
          id: potentialLiveStream.id,
          publishedAt: potentialLiveStream.publishedAt,
          minutesAgo: Math.round(
            (Date.now() - new Date(potentialLiveStream.publishedAt).getTime()) /
              (60 * 1000)
          ),
        }
      );
    } else {
      // Log the most recent videos to help debug
      const recentVideos = videos.slice(0, 3);
      console.log(
        `RSS: No live streams found. Most recent videos:`,
        recentVideos.map((v) => ({
          title: v.title,
          publishedAt: v.publishedAt,
          minutesAgo: Math.round(
            (Date.now() - new Date(v.publishedAt).getTime()) / (60 * 1000)
          ),
        }))
      );
    }

    // Log all videos with their dates for debugging
    console.log("RSS - All videos sorted by date:");
    videos.slice(0, 5).forEach((video, index) => {
      console.log(`  ${index + 1}. "${video.title}" (${video.publishedAt})`);
    });

    // Use the first video (newest) as the recent past live stream
    // This is more reliable than trying to detect past live streams
    const recentPastLiveStream = videos.length > 0 ? videos[0] : null;

    if (recentPastLiveStream) {
      console.log(
        `RSS - Using newest video as recent past live stream: "${recentPastLiveStream.title}" (${recentPastLiveStream.publishedAt})`
      );
    }

    return {
      videos,
      liveStream: potentialLiveStream,
      recentPastLiveStream,
    };
  } catch (error) {
    console.error("Error fetching YouTube RSS feed:", error);
    return { videos: [], liveStream: null, recentPastLiveStream: null };
  }
}
