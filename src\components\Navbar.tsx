"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { Menu, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { scrollToSection } from "@/lib/scroll";
import { ThemeToggle } from "@/components/ThemeToggle";
import { usePathname } from "next/navigation";

const navLinks = [
  { href: "home", label: "Home" },
  { href: "about", label: "About" },
  // Ministries dropdown will be handled separately
  { href: "events", label: "Events" },
  { href: "sermons", label: "Sermons" },
  { href: "contact", label: "Contact" },
];

const externalLinks = [
  { href: "/blog", label: "Blog" },
  { href: "/churches", label: "Church Locations" },
];

export function Navbar() {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeSection, setActiveSection] = useState("home");
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Check if we're on the landing page (root path)
  const isLandingPage = pathname === "/";

  // Handle scroll effect and active section
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);

      // Only track active sections on the landing page
      if (isLandingPage) {
        // Determine active section
        const sections = navLinks.map((link) => link.href);

        // Check each section
        for (const section of sections) {
          const element = document.getElementById(section);
          if (!element) continue;

          const rect = element.getBoundingClientRect();
          const offset = 100; // Adjust this value as needed

          // If the element is in view (with offset)
          if (rect.top <= offset && rect.bottom > offset) {
            setActiveSection(section);
            break;
          }
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [isLandingPage]); // Add isLandingPage as a dependency

  // Handle smooth scrolling
  const handleNavClick = (
    e: React.MouseEvent<HTMLAnchorElement>,
    sectionId: string
  ) => {
    e.preventDefault();
    scrollToSection(sectionId, 80); // 80px offset for the navbar
    setActiveSection(sectionId);
    setIsOpen(false); // Close mobile menu if open
  };

  return (
    <nav
      className={cn(
        "fixed top-0 w-full z-50 transition-all duration-300",
        isScrolled
          ? "bg-background/95 backdrop-blur-sm shadow-md"
          : "bg-background/50 backdrop-blur-sm"
      )}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Brand */}
          <Link
            href="/"
            className="flex items-center gap-2 hover:opacity-90 transition-opacity"
          >
            <Image
              src="/logo/logo transparent[64].png"
              alt="Revival Fire Missions"
              width={40}
              height={40}
              className="h-10 w-auto"
            />
            <span className="text-xl font-bold text-primary hidden sm:block">
              Revival Fire Missions
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden xl:flex items-center gap-4 xl:gap-6 flex-wrap">
            {isLandingPage
              ? navLinks.map((link) =>
                  link.label === "Ministries" ? null : (
                    <a
                      key={link.href}
                      href={`#${link.href}`}
                      onClick={(e) => handleNavClick(e, link.href)}
                      className={cn(
                        "text-foreground/90 hover:text-primary transition-colors px-2 py-1 rounded-md text-base xl:text-lg",
                        activeSection === link.href &&
                          "text-primary font-medium bg-primary/10"
                      )}
                      style={{ whiteSpace: "nowrap" }}
                    >
                      {link.label}
                    </a>
                  )
                )
              : externalLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className={cn(
                      "text-foreground/90 hover:text-primary transition-colors px-2 py-1 rounded-md text-base xl:text-lg",
                      pathname.startsWith(link.href) &&
                        "text-primary font-medium bg-primary/10"
                    )}
                    style={{ whiteSpace: "nowrap" }}
                  >
                    {link.label}
                  </Link>
                ))}
            {/* Ministries Dropdown */}
            {isLandingPage && (
              <div className="relative" tabIndex={0}>
                <button
                  className="text-foreground/90 hover:text-primary transition-colors px-2 py-1 rounded-md text-base xl:text-lg flex items-center gap-1 focus:outline-none"
                  type="button"
                  aria-haspopup="true"
                  aria-expanded={dropdownOpen ? "true" : "false"}
                  onClick={() => setDropdownOpen((open) => !open)}
                  onBlur={(e) => {
                    // Only close if focus leaves the dropdown
                    if (
                      !e.currentTarget.parentElement?.contains(e.relatedTarget)
                    ) {
                      setDropdownOpen(false);
                    }
                  }}
                >
                  Churches
                  <svg
                    className={`w-4 h-4 transition-transform ${
                      dropdownOpen ? "rotate-180" : ""
                    }`}
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>
                <div
                  className={`absolute left-0 mt-2 min-w-[200px] bg-background border border-border rounded-md shadow-lg transition-all duration-200 z-50 ${
                    dropdownOpen
                      ? "opacity-100 scale-100 pointer-events-auto"
                      : "opacity-0 scale-95 pointer-events-none"
                  }`}
                  tabIndex={-1}
                >
                  <div className="flex flex-col py-2">
                    <a
                      href="churches"
                      className="px-4 py-2 hover:bg-accent text-foreground/90 transition-colors"
                    >
                      Locations
                    </a>
                    <a
                      href="timesandseasons"
                      className="px-4 py-2 hover:bg-accent text-foreground/90 transition-colors"
                    >
                      Times and Seasons
                    </a>
                    <a
                      href="alpha"
                      className="px-4 py-2 hover:bg-accent text-foreground/90 transition-colors"
                    >
                      Alpha Revival Centre
                    </a>
                    <a
                      href="eastgate"
                      className="px-4 py-2 hover:bg-accent text-foreground/90 transition-colors"
                    >
                      Eastgate Revival Centre
                    </a>
                    {/* Nested Dropdown for Others */}
                    <div className="relative group/others" tabIndex={0}>
                      <button
                        className="flex items-center justify-between w-full px-4 py-2 hover:bg-accent text-foreground/90 transition-colors focus:outline-none"
                        type="button"
                        aria-haspopup="true"
                        aria-expanded="false"
                      >
                        Others
                        <svg
                          className="w-4 h-4 ml-2 rotate-180"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </button>
                      <div className="absolute right-full top-0 mt-0 min-w-[180px] bg-background border border-border rounded-md shadow-lg opacity-0 scale-95 group-hover/others:opacity-100 group-hover/others:scale-100 group-focus-within/others:opacity-100 group-focus-within/others:scale-100 pointer-events-none group-hover/others:pointer-events-auto group-focus-within/others:pointer-events-auto transition-all duration-200 z-50">
                        <div className="flex flex-col py-2">
                          <a
                            href="#youth"
                            className="px-4 py-2 hover:bg-accent text-foreground/90 transition-colors"
                          >
                            Revival Fire Missions Mumbwa
                          </a>
                          <a
                            href="#childrens"
                            className="px-4 py-2 hover:bg-accent text-foreground/90 transition-colors"
                          >
                            Revival Fire Missions Ndola
                          </a>
                          {/* Add more as needed */}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div className="flex items-center gap-2 ml-2">
              <ThemeToggle />
              <Button size="sm" variant="default" className="min-w-[90px]">
                Give Now
              </Button>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="xl:hidden p-2 rounded-md hover:bg-accent transition-colors focus:outline-none focus:ring-2 focus:ring-primary"
            aria-label="Toggle menu"
            type="button"
          >
            {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        <div
          className={cn(
            "xl:hidden fixed top-16 left-0 w-full bg-background/95 backdrop-blur-md shadow-lg transition-all duration-300 ease-in-out overflow-y-auto z-40",
            isOpen ? "max-h-[90vh] opacity-100" : "max-h-0 opacity-0"
          )}
          style={{ minHeight: isOpen ? "calc(100vh - 4rem)" : undefined }}
        >
          <div className="flex flex-col gap-2 px-2 pb-6 pt-4">
            {isLandingPage
              ? navLinks
                  .filter((l) => l.label !== "Ministries")
                  .map((l) => (
                    <a
                      key={l.href}
                      href={`#${l.href}`}
                      className={cn(
                        "text-foreground/90 hover:text-primary transition-colors py-2 rounded-md text-base",
                        activeSection === l.href &&
                          "text-primary font-medium bg-primary/10"
                      )}
                      onClick={(e) => {
                        handleNavClick(e, l.href);
                        setIsOpen(false);
                      }}
                    >
                      {l.label}
                    </a>
                  ))
              : externalLinks.map((el) => (
                  <Link
                    key={el.href}
                    href={el.href}
                    className={cn(
                      "text-foreground/90 hover:text-primary transition-colors py-2 rounded-md text-base",
                      pathname.startsWith(el.href) &&
                        "text-primary font-medium bg-primary/10"
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    {el.label}
                  </Link>
                ))}
            {/* Ministries Dropdown for mobile */}
            {isLandingPage && (
              <details className="group">
                <summary className="text-foreground/90 hover:text-primary transition-colors py-2 rounded-md text-base flex items-center gap-1 cursor-pointer select-none">
                  Ministries
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </summary>
                <div className="flex flex-col bg-background border border-border rounded-md shadow-lg mt-1">
                  <a
                    href="churches"
                    className="px-4 py-2 hover:bg-accent text-foreground/90 transition-colors"
                  >
                    Locations
                  </a>
                  <a
                    href="timesandseasons"
                    className="px-4 py-2 hover:bg-accent text-foreground/90 transition-colors"
                  >
                    Times and Seasons
                  </a>
                  <a
                    href="alpha"
                    className="px-4 py-2 hover:bg-accent text-foreground/90 transition-colors"
                  >
                    Alpha Revival Centre
                  </a>
                  <a
                    href="eastgate"
                    className="px-4 py-2 hover:bg-accent text-foreground/90 transition-colors"
                  >
                    Eastgate Revival Centre
                  </a>
                  {/* Nested dropdown for Others in mobile */}
                  <details className="group">
                    <summary className="flex items-center justify-between w-full px-4 py-2 hover:bg-accent text-foreground/90 transition-colors cursor-pointer select-none">
                      Others
                      <svg
                        className="w-4 h-4 ml-2 rotate-180"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </summary>
                    <div className="flex flex-col bg-background border border-border rounded-md shadow-lg mt-1 ml-2">
                      <a
                        href="#childrens"
                        className="px-4 py-2 hover:bg-accent text-foreground/90 transition-colors"
                      >
                        Revival Fire Missions Mumbwa
                      </a>
                      <a
                        href="#youth"
                        className="px-4 py-2 hover:bg-accent text-foreground/90 transition-colors"
                      >
                        Revival Fire Missions Ndola
                      </a>
                      {/* Add more as needed */}
                    </div>
                  </details>
                </div>
              </details>
            )}
            <div className="flex flex-col gap-2 mt-2">
              <div className="flex justify-center py-2">
                <ThemeToggle />
              </div>
              <Button
                size="sm"
                variant="default"
                className="w-full min-w-[90px]"
              >
                Give Now
              </Button>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
