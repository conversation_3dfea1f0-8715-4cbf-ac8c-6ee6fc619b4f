/**
 * API endpoint for archived live streams (replays)
 * Returns past live streams that are available for replay
 */

import { NextResponse } from "next/server";
import { getArchivedLiveStreams } from "@/lib/live-stream-archive";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const maxResults = parseInt(searchParams.get("limit") || "20", 10);
    const page = parseInt(searchParams.get("page") || "1", 10);
    const onlyReady = searchParams.get("ready") === "true";

    const channelId = process.env.YOUTUBE_CHANNEL_ID;
    if (!channelId) {
      return NextResponse.json(
        { error: "Channel ID not configured" },
        { status: 500 }
      );
    }

    console.log(`📼 Fetching archived live streams (page ${page}, limit ${maxResults})`);

    // Get archived streams
    const allArchivedStreams = await getArchivedLiveStreams(channelId, maxResults * 2);

    // Filter by ready status if requested
    let filteredStreams = allArchivedStreams;
    if (onlyReady) {
      filteredStreams = allArchivedStreams.filter(stream => stream.replayAvailable);
      console.log(`📼 Filtered to ${filteredStreams.length} ready replays`);
    }

    // Paginate results
    const startIndex = (page - 1) * maxResults;
    const endIndex = startIndex + maxResults;
    const paginatedStreams = filteredStreams.slice(startIndex, endIndex);

    // Calculate statistics
    const stats = {
      totalArchived: allArchivedStreams.length,
      totalReady: allArchivedStreams.filter(s => s.replayAvailable).length,
      totalProcessing: allArchivedStreams.filter(s => s.processingStatus === 'processing').length,
      totalFailed: allArchivedStreams.filter(s => s.processingStatus === 'failed').length,
    };

    const response = {
      archivedStreams: paginatedStreams,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(filteredStreams.length / maxResults),
        totalItems: filteredStreams.length,
        itemsPerPage: maxResults,
        hasNextPage: endIndex < filteredStreams.length,
        hasPreviousPage: page > 1,
      },
      statistics: stats,
      filters: {
        onlyReady,
      },
      timestamp: new Date().toISOString(),
    };

    // Set cache headers (cache for 5 minutes since processing status can change)
    const headers = new Headers();
    headers.set("Cache-Control", "public, max-age=300, s-maxage=300");
    headers.set("X-Response-Time", new Date().toISOString());

    return NextResponse.json(response, { headers });

  } catch (error) {
    console.error("❌ Error fetching archived streams:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch archived streams",
        details: error instanceof Error ? error.message : String(error),
        archivedStreams: [],
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST endpoint to manually trigger archiving of a specific video
 * Useful for retroactively archiving streams that weren't automatically processed
 */
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { videoId, forceReprocess } = body;

    if (!videoId) {
      return NextResponse.json(
        { error: "Video ID is required" },
        { status: 400 }
      );
    }

    const channelId = process.env.YOUTUBE_CHANNEL_ID;
    if (!channelId) {
      return NextResponse.json(
        { error: "Channel ID not configured" },
        { status: 500 }
      );
    }

    console.log(`📼 Manual archive request for video: ${videoId}`);

    // Import the archiving function
    const { archiveEndedLiveStream } = await import("@/lib/live-stream-archive");
    const { youtube } = await import("googleapis");
    
    const youtubeApi = youtube("v3");
    const apiKey = process.env.YOUTUBE_API_KEY;

    if (!apiKey) {
      return NextResponse.json(
        { error: "YouTube API key not configured" },
        { status: 500 }
      );
    }

    // Get video details to create a YouTubeVideo object
    const videoResponse = await youtubeApi.videos.list({
      key: apiKey,
      part: ["snippet", "liveStreamingDetails"],
      id: [videoId],
    });

    if (!videoResponse.data.items || videoResponse.data.items.length === 0) {
      return NextResponse.json(
        { error: "Video not found" },
        { status: 404 }
      );
    }

    const video = videoResponse.data.items[0];
    
    // Verify this belongs to the correct channel
    if (video.snippet?.channelId !== channelId) {
      return NextResponse.json(
        { error: "Video does not belong to the configured channel" },
        { status: 403 }
      );
    }

    // Create YouTubeVideo object
    const youtubeVideo = {
      id: video.id || "",
      title: video.snippet?.title || "",
      description: video.snippet?.description || "",
      thumbnail: video.snippet?.thumbnails?.high?.url || "",
      publishedAt: video.snippet?.publishedAt || "",
      channelTitle: video.snippet?.channelTitle || "",
      isLive: false,
      isPastLive: !!(video.liveStreamingDetails?.actualStartTime && video.liveStreamingDetails?.actualEndTime),
    };

    // Archive the stream
    const archiveResult = await archiveEndedLiveStream(youtubeVideo);

    if (archiveResult.success) {
      console.log(`✅ Manual archive successful for: ${videoId}`);
      
      return NextResponse.json({
        success: true,
        message: "Stream archived successfully",
        archivedStream: archiveResult.archivedStream,
        processingTimeEstimate: archiveResult.processingTimeEstimate,
        timestamp: new Date().toISOString(),
      });
    } else {
      console.error(`❌ Manual archive failed for: ${videoId} - ${archiveResult.error}`);
      
      return NextResponse.json(
        {
          success: false,
          error: archiveResult.error,
          timestamp: new Date().toISOString(),
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error("❌ Error in manual archive request:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Manual archive request failed",
        details: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
