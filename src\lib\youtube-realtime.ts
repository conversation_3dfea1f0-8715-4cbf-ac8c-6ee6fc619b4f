/**
 * Real-time YouTube live stream monitoring with WebSocket support
 * Features: Live updates, viewer count tracking, stream health monitoring
 */

import { EventEmitter } from 'events';
import { getLiveStreamStatus, YouTubeVideo, LiveStreamAnalytics } from './youtube-live';

export interface LiveStreamEvent {
  type: 'stream_started' | 'stream_ended' | 'viewer_count_updated' | 'stream_health_changed';
  timestamp: string;
  data: {
    stream?: YouTubeVideo;
    viewerCount?: number;
    previousViewerCount?: number;
    health?: 'excellent' | 'good' | 'poor' | 'critical';
    analytics?: LiveStreamAnalytics;
  };
}

export interface StreamHealthMetrics {
  viewerTrend: 'increasing' | 'stable' | 'decreasing';
  averageViewers: number;
  peakViewers: number;
  streamDuration: number;
  lastUpdate: string;
  healthScore: number; // 0-100
}

class YouTubeRealtimeMonitor extends EventEmitter {
  private channelId: string;
  private isMonitoring = false;
  private currentStream: YouTubeVideo | null = null;
  private viewerHistory: Array<{ timestamp: number; count: number }> = [];
  private healthMetrics: StreamHealthMetrics | null = null;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private fastPollingInterval: NodeJS.Timeout | null = null;
  
  // Monitoring configuration
  private readonly NORMAL_POLL_INTERVAL = 60 * 1000; // 1 minute when not live
  private readonly FAST_POLL_INTERVAL = 15 * 1000; // 15 seconds when live
  private readonly VIEWER_HISTORY_LIMIT = 100; // Keep last 100 viewer count readings
  
  constructor(channelId: string) {
    super();
    this.channelId = channelId;
  }

  /**
   * Start monitoring the channel for live streams
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      console.log('Real-time monitoring already active');
      return;
    }

    console.log(`🚀 Starting real-time monitoring for channel ${this.channelId}`);
    this.isMonitoring = true;

    // Initial check
    await this.checkLiveStatus();

    // Set up regular monitoring
    this.monitoringInterval = setInterval(async () => {
      if (this.isMonitoring) {
        await this.checkLiveStatus();
      }
    }, this.NORMAL_POLL_INTERVAL);

    this.emit('monitoring_started', { channelId: this.channelId });
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    console.log('🛑 Stopping real-time monitoring');
    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    if (this.fastPollingInterval) {
      clearInterval(this.fastPollingInterval);
      this.fastPollingInterval = null;
    }

    this.emit('monitoring_stopped', { channelId: this.channelId });
  }

  /**
   * Check current live status and emit events for changes
   */
  private async checkLiveStatus(): Promise<void> {
    try {
      const status = await getLiveStreamStatus(this.channelId, false);
      const wasLive = !!this.currentStream;
      const isNowLive = status.isLive;

      // Stream started
      if (!wasLive && isNowLive && status.liveStream) {
        console.log('🔴 Stream started detected!');
        this.currentStream = status.liveStream;
        this.startFastPolling();
        this.initializeHealthMetrics();
        
        this.emit('stream_started', {
          type: 'stream_started',
          timestamp: new Date().toISOString(),
          data: {
            stream: status.liveStream,
            analytics: status.analytics
          }
        } as LiveStreamEvent);
      }

      // Stream ended
      if (wasLive && !isNowLive) {
        console.log('⚫ Stream ended detected!');
        const endedStream = this.currentStream;
        this.currentStream = null;
        this.stopFastPolling();
        
        this.emit('stream_ended', {
          type: 'stream_ended',
          timestamp: new Date().toISOString(),
          data: {
            stream: endedStream,
            analytics: this.calculateFinalAnalytics()
          }
        } as LiveStreamEvent);
        
        this.resetHealthMetrics();
      }

      // Viewer count update (only when live)
      if (isNowLive && status.liveStream && this.currentStream) {
        const newViewerCount = status.liveStream.concurrentViewers || 0;
        const oldViewerCount = this.currentStream.concurrentViewers || 0;
        
        if (newViewerCount !== oldViewerCount) {
          this.updateViewerHistory(newViewerCount);
          this.updateHealthMetrics();
          this.currentStream = status.liveStream;
          
          this.emit('viewer_count_updated', {
            type: 'viewer_count_updated',
            timestamp: new Date().toISOString(),
            data: {
              stream: status.liveStream,
              viewerCount: newViewerCount,
              previousViewerCount: oldViewerCount
            }
          } as LiveStreamEvent);
        }
      }

    } catch (error) {
      console.error('Error in real-time monitoring:', error);
      this.emit('monitoring_error', { error, timestamp: new Date().toISOString() });
    }
  }

  /**
   * Start fast polling when live
   */
  private startFastPolling(): void {
    if (this.fastPollingInterval) return;
    
    console.log('⚡ Starting fast polling for live stream');
    this.fastPollingInterval = setInterval(async () => {
      if (this.isMonitoring && this.currentStream) {
        await this.checkLiveStatus();
      }
    }, this.FAST_POLL_INTERVAL);
  }

  /**
   * Stop fast polling
   */
  private stopFastPolling(): void {
    if (this.fastPollingInterval) {
      clearInterval(this.fastPollingInterval);
      this.fastPollingInterval = null;
      console.log('⏸️ Stopped fast polling');
    }
  }

  /**
   * Update viewer count history
   */
  private updateViewerHistory(viewerCount: number): void {
    this.viewerHistory.push({
      timestamp: Date.now(),
      count: viewerCount
    });

    // Keep only recent history
    if (this.viewerHistory.length > this.VIEWER_HISTORY_LIMIT) {
      this.viewerHistory = this.viewerHistory.slice(-this.VIEWER_HISTORY_LIMIT);
    }
  }

  /**
   * Initialize health metrics when stream starts
   */
  private initializeHealthMetrics(): void {
    this.healthMetrics = {
      viewerTrend: 'stable',
      averageViewers: 0,
      peakViewers: 0,
      streamDuration: 0,
      lastUpdate: new Date().toISOString(),
      healthScore: 100
    };
  }

  /**
   * Update health metrics based on current data
   */
  private updateHealthMetrics(): void {
    if (!this.healthMetrics || this.viewerHistory.length < 2) return;

    const recent = this.viewerHistory.slice(-10); // Last 10 readings
    const counts = recent.map(h => h.count);
    
    // Calculate metrics
    this.healthMetrics.averageViewers = Math.round(counts.reduce((a, b) => a + b, 0) / counts.length);
    this.healthMetrics.peakViewers = Math.max(this.healthMetrics.peakViewers, ...counts);
    
    // Determine trend
    if (counts.length >= 3) {
      const firstThird = counts.slice(0, Math.floor(counts.length / 3));
      const lastThird = counts.slice(-Math.floor(counts.length / 3));
      const firstAvg = firstThird.reduce((a, b) => a + b, 0) / firstThird.length;
      const lastAvg = lastThird.reduce((a, b) => a + b, 0) / lastThird.length;
      
      const change = (lastAvg - firstAvg) / firstAvg;
      if (change > 0.1) this.healthMetrics.viewerTrend = 'increasing';
      else if (change < -0.1) this.healthMetrics.viewerTrend = 'decreasing';
      else this.healthMetrics.viewerTrend = 'stable';
    }

    // Calculate health score
    this.healthMetrics.healthScore = this.calculateHealthScore();
    this.healthMetrics.lastUpdate = new Date().toISOString();

    // Emit health change if significant
    const health = this.getHealthLevel(this.healthMetrics.healthScore);
    this.emit('stream_health_changed', {
      type: 'stream_health_changed',
      timestamp: new Date().toISOString(),
      data: {
        health,
        analytics: this.calculateCurrentAnalytics()
      }
    } as LiveStreamEvent);
  }

  /**
   * Calculate health score (0-100)
   */
  private calculateHealthScore(): number {
    if (!this.healthMetrics || this.viewerHistory.length === 0) return 100;

    let score = 100;
    
    // Penalize for decreasing viewers
    if (this.healthMetrics.viewerTrend === 'decreasing') score -= 20;
    
    // Bonus for increasing viewers
    if (this.healthMetrics.viewerTrend === 'increasing') score += 10;
    
    // Consider viewer stability
    const recent = this.viewerHistory.slice(-5).map(h => h.count);
    if (recent.length > 1) {
      const variance = this.calculateVariance(recent);
      const avgViewers = recent.reduce((a, b) => a + b, 0) / recent.length;
      const stability = 1 - (Math.sqrt(variance) / avgViewers);
      score *= Math.max(0.5, stability);
    }

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Calculate variance for stability measurement
   */
  private calculateVariance(numbers: number[]): number {
    const avg = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    const squareDiffs = numbers.map(n => Math.pow(n - avg, 2));
    return squareDiffs.reduce((a, b) => a + b, 0) / numbers.length;
  }

  /**
   * Get health level from score
   */
  private getHealthLevel(score: number): 'excellent' | 'good' | 'poor' | 'critical' {
    if (score >= 80) return 'excellent';
    if (score >= 60) return 'good';
    if (score >= 40) return 'poor';
    return 'critical';
  }

  /**
   * Calculate current analytics
   */
  private calculateCurrentAnalytics(): LiveStreamAnalytics | undefined {
    if (!this.healthMetrics || !this.currentStream) return undefined;

    return {
      peakViewers: this.healthMetrics.peakViewers,
      averageViewers: this.healthMetrics.averageViewers,
      streamDuration: this.currentStream.estimatedDuration || 0,
      detectionLatency: 0,
      verificationMethod: 'realtime',
      apiCallsUsed: 0
    };
  }

  /**
   * Calculate final analytics when stream ends
   */
  private calculateFinalAnalytics(): LiveStreamAnalytics | undefined {
    return this.calculateCurrentAnalytics();
  }

  /**
   * Reset health metrics
   */
  private resetHealthMetrics(): void {
    this.healthMetrics = null;
    this.viewerHistory = [];
  }

  /**
   * Get current stream info
   */
  getCurrentStream(): YouTubeVideo | null {
    return this.currentStream;
  }

  /**
   * Get current health metrics
   */
  getHealthMetrics(): StreamHealthMetrics | null {
    return this.healthMetrics;
  }

  /**
   * Get viewer history
   */
  getViewerHistory(): Array<{ timestamp: number; count: number }> {
    return [...this.viewerHistory];
  }

  /**
   * Check if currently monitoring
   */
  isActive(): boolean {
    return this.isMonitoring;
  }
}

// Export singleton instance
let monitorInstance: YouTubeRealtimeMonitor | null = null;

export function getRealtimeMonitor(channelId: string): YouTubeRealtimeMonitor {
  if (!monitorInstance || monitorInstance['channelId'] !== channelId) {
    if (monitorInstance) {
      monitorInstance.stopMonitoring();
    }
    monitorInstance = new YouTubeRealtimeMonitor(channelId);
  }
  return monitorInstance;
}

export { YouTubeRealtimeMonitor };
