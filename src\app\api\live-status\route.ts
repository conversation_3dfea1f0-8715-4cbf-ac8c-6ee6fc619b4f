import { NextResponse } from "next/server";
import { getLiveStreamStatus } from "@/lib/youtube-live";
import { getVideosFromRSS } from "@/lib/youtube-rss";
import { checkIfChannelIsLive } from "@/lib/youtube-direct";
import { YouTubeApiResponse } from "@/types/youtube";

// Define route segment config for Next.js 15 caching
export const dynamic = "force-dynamic"; // Make this route dynamic by default
export const revalidate = 0; // Revalidate on every request

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const debug = searchParams.get("debug") === "true";

    // Get channel ID from environment variables
    const channelId = process.env.YOUTUBE_CHANNEL_ID;
    if (!channelId) {
      throw new Error("YouTube channel ID not configured");
    }

    console.log("Checking live status with multiple methods");
    console.log("Current server time:", new Date().toISOString());

    // Set cache control headers to prevent caching
    const headers = new Headers();
    headers.set(
      "Cache-Control",
      "no-store, no-cache, must-revalidate, proxy-revalidate"
    );
    headers.set("Pragma", "no-cache");
    headers.set("Expires", "0");
    headers.set("X-Response-Time", new Date().toISOString());

    // Prepare response data
    let responseData: YouTubeApiResponse;

    // STEP 1: First try direct check (most reliable for live detection)
    console.log("STEP 1: Performing direct live check");
    const directCheck = await checkIfChannelIsLive(channelId);

    // If direct check says we're live, use that result
    if (directCheck.isLive && directCheck.liveStream) {
      console.log("DIRECT CHECK CONFIRMS CHANNEL IS LIVE!");

      // Get videos from RSS feed for the rest of the content
      const rssData = await getVideosFromRSS(channelId);

      // Override the liveStream with our direct check result
      responseData = {
        isLive: true,
        liveStream: directCheck.liveStream,
        recentPastLiveStream:
          rssData.recentPastLiveStream ||
          (rssData.videos.length > 0 ? rssData.videos[0] : null),
        sermons: rssData.videos,
        timestamp: new Date().toISOString(),
        cached: false,
        dataSource: "direct",
      };

      if (debug) {
        responseData.debug = {
          liveStreamFound: true,
          liveStreamTitle: directCheck.liveStream.title,
          liveStreamDetails: {
            id: directCheck.liveStream.id,
            title: directCheck.liveStream.title,
            publishedAt: directCheck.liveStream.publishedAt,
          },
          detectionMethod: "direct",
          serverTime: new Date().toISOString(),
        };
      }

      return NextResponse.json(responseData, { headers });
    }

    // STEP 2: If direct check didn't find a live stream, try RSS
    console.log(
      "STEP 2: Direct check didn't find a live stream, using RSS feed"
    );
    const rssData = await getVideosFromRSS(channelId);

    // If RSS found a live stream, use that
    if (rssData.liveStream) {
      console.log("RSS found a live stream!");

      // Ensure we have a recent past live stream
      let recentPastLiveStream = rssData.recentPastLiveStream;
      if (!recentPastLiveStream && rssData.videos.length > 0) {
        recentPastLiveStream = rssData.videos[0];
      }

      responseData = {
        isLive: true,
        liveStream: rssData.liveStream,
        recentPastLiveStream: recentPastLiveStream,
        sermons: rssData.videos,
        timestamp: new Date().toISOString(),
        cached: false,
        dataSource: "rss",
      };

      if (debug) {
        responseData.debug = {
          liveStreamFound: true,
          liveStreamTitle: rssData.liveStream.title,
          liveStreamDetails: {
            id: rssData.liveStream.id,
            title: rssData.liveStream.title,
            publishedAt: rssData.liveStream.publishedAt,
          },
          detectionMethod: "rss",
          serverTime: new Date().toISOString(),
        };
      }

      return NextResponse.json(responseData, { headers });
    }

    // STEP 3: If we get here, no live stream was found
    console.log("STEP 3: No live stream found via any method");

    // Ensure we have a recent past live stream
    // First, make sure videos are properly sorted by date
    rssData.videos.sort((a, b) => {
      const dateA = new Date(a.publishedAt).getTime();
      const dateB = new Date(b.publishedAt).getTime();
      return dateB - dateA; // Newest first
    });

    // Log all videos with their dates for debugging
    console.log("All videos sorted by date:");
    rssData.videos.slice(0, 5).forEach((video, index) => {
      console.log(`  ${index + 1}. "${video.title}" (${video.publishedAt})`);
    });

    // Use the first video (newest) as the recent past live stream
    let recentPastLiveStream =
      rssData.videos.length > 0 ? rssData.videos[0] : null;

    if (recentPastLiveStream) {
      console.log(
        `Using newest video as recent past live stream: "${recentPastLiveStream.title}" (${recentPastLiveStream.publishedAt})`
      );
    } else {
      console.log("No videos available for recent past live stream");
    }

    responseData = {
      isLive: false,
      liveStream: null,
      recentPastLiveStream: recentPastLiveStream,
      sermons: rssData.videos,
      timestamp: new Date().toISOString(),
      cached: false,
      dataSource: "rss",
    };

    // Add debug information if requested
    if (debug) {
      responseData.debug = {
        liveStreamFound: false,
        detectionMethod: "none",
        serverTime: new Date().toISOString(),
        totalVideos: rssData.videos.length,
      };
    }

    return NextResponse.json(responseData, { headers });
  } catch (error) {
    console.error("Error in live status API:", error);

    // Try to get data from RSS as a last resort
    try {
      console.log("Critical error, trying RSS as last resort");
      const channelId = process.env.YOUTUBE_CHANNEL_ID;

      if (channelId) {
        const rssData = await getVideosFromRSS(channelId);

        // Ensure we have a recent past live stream from RSS data
        let recentPastLiveStream = rssData.recentPastLiveStream;

        // If no past live stream was found but we have videos, use the most recent one
        if (!recentPastLiveStream && rssData.videos.length > 0) {
          recentPastLiveStream = rssData.videos[0];
          console.log(
            `Last Resort - RSS Fallback: No past live streams found, using most recent video as fallback: "${recentPastLiveStream.title}"`
          );
        }

        const responseData = {
          isLive: !!rssData.liveStream,
          liveStream: rssData.liveStream,
          recentPastLiveStream: recentPastLiveStream,
          sermons: rssData.videos,
          timestamp: new Date().toISOString(),
          dataSource: "rss",
          error: "API error, using RSS fallback",
          errorDetails: error instanceof Error ? error.message : String(error),
        };

        return NextResponse.json(responseData);
      }
    } catch (rssError) {
      console.error("RSS fallback also failed:", rssError);
    }

    // If everything fails, return a proper error response
    let errorMessage = "Failed to check live status";
    let statusCode = 500;
    let errorDetails = null;

    if (error instanceof Error) {
      console.error("Error details:", error.message, error.stack);
      errorDetails = {
        message: error.message,
        stack: error.stack,
      };

      if (error.message.includes("quota")) {
        errorMessage =
          "YouTube API quota exceeded. Fallback methods also failed.";
        statusCode = 429; // Too Many Requests
      }
    }

    // Return error response
    return NextResponse.json(
      {
        error: errorMessage,
        errorDetails: errorDetails,
        timestamp: new Date().toISOString(),
        isLive: false,
        liveStream: null,
        sermons: [], // Return empty array instead of null
      },
      { status: statusCode }
    );
  }
}
