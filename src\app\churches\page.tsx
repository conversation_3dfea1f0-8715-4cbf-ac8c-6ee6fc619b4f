"use client";

import { MapPin, ExternalLink } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { getClient } from "@/sanity/client";
import { getBranchesQuery } from "@/sanity/queries/branchQueries";

// Define types for better type safety
interface Meeting {
  title: string;
  day: string; // e.g., "Sunday"
  time: string; // e.g., "10:00 AM - 12:00 PM"
  description?: string;
}

interface Branch {
  name: string;
  address: string;
  note?: string;
  mapUrl: string;
  embedUrl: string;
  email?: string;
  contact?: string;
  contactPerson?: string;
  hours?: Partial<
    Record<
      | "Sunday"
      | "Monday"
      | "Tuesday"
      | "Wednesday"
      | "Thursday"
      | "Friday"
      | "Saturday",
      string
    >
  >;
  meetings?: Meeting[];
}

function copyToClipboard(text: string, setCopied: (v: boolean) => void) {
  navigator.clipboard.writeText(text).then(() => {
    setCopied(true);
    setTimeout(() => setCopied(false), 1500);
  });
}

export default function ChurchesPage() {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copiedIdx, setCopiedIdx] = useState<{
    type: string;
    idx: number;
  } | null>(null);
  const [modalIdx, setModalIdx] = useState<number | null>(null);

  // Days of the week for calendar
  const days = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ] as const;

  useEffect(() => {
    async function fetchBranches() {
      try {
        const data = await getClient().fetch(getBranchesQuery);
        setBranches(data);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (err) {
        setError("Failed to load branches");
      } finally {
        setLoading(false);
      }
    }
    fetchBranches();
  }, []);

  if (loading) {
    return <div className="text-center py-20">Loading branches...</div>;
  }
  if (error) {
    return <div className="text-center py-20 text-red-500">{error}</div>;
  }

  return (
    <section className="py-20 bg-secondary/30 min-h-screen">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold mb-4">Our Church Branches</h2>
          <p className="text-lg text-muted-foreground">
            Find a Times and Seasons Church branch near you
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          {branches.map((branch, idx) => (
            <div
              key={idx}
              className="bg-background rounded-xl shadow-lg border border-border p-6 flex flex-col gap-4"
            >
              <h3 className="text-2xl font-semibold mb-2 flex items-center gap-2">
                <MapPin className="h-6 w-6 text-primary" />
                {branch.name}
              </h3>
              <p className="text-muted-foreground">{branch.address}</p>
              <p className="text-xs text-muted-foreground">{branch.note}</p>
              {(branch.email || branch.contact) && (
                <div className="flex gap-4 items-center mt-2">
                  {branch.email && (
                    <button
                      className="text-primary underline hover:text-primary/80 text-sm relative"
                      onClick={() =>
                        copyToClipboard(
                          `${branch.email}${
                            branch.contactPerson
                              ? ` (Contact: ${branch.contactPerson})`
                              : ""
                          }`,
                          () => setCopiedIdx({ type: "email", idx })
                        )
                      }
                      title={`Copy email${
                        branch.contactPerson
                          ? ` for ${branch.contactPerson}`
                          : ""
                      }`}
                    >
                      {branch.email}
                      {copiedIdx &&
                        copiedIdx.type === "email" &&
                        copiedIdx.idx === idx && (
                          <span className="absolute left-1/2 -translate-x-1/2 top-full mt-1 text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded shadow">
                            Copied!
                          </span>
                        )}
                    </button>
                  )}
                  {branch.contact && (
                    <button
                      className="text-primary underline hover:text-primary/80 text-sm relative"
                      onClick={() =>
                        copyToClipboard(
                          `${branch.contact}${
                            branch.contactPerson
                              ? ` (Contact: ${branch.contactPerson})`
                              : ""
                          }`,
                          () => setCopiedIdx({ type: "contact", idx })
                        )
                      }
                      title={`Copy contact${
                        branch.contactPerson
                          ? ` for ${branch.contactPerson}`
                          : ""
                      }`}
                    >
                      {branch.contact}
                      {copiedIdx &&
                        copiedIdx.type === "contact" &&
                        copiedIdx.idx === idx && (
                          <span className="absolute left-1/2 -translate-x-1/2 top-full mt-1 text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded shadow">
                            Copied!
                          </span>
                        )}
                    </button>
                  )}
                  {branch.contactPerson && (
                    <span className="text-xs text-muted-foreground">
                      ({branch.contactPerson})
                    </span>
                  )}
                </div>
              )}
              <div className="rounded-xl overflow-hidden border border-border h-[250px] w-full">
                <iframe
                  src={branch.embedUrl}
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen={true}
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title={branch.name + " Location"}
                  aria-label={branch.name + " Google Map"}
                />
              </div>
              <Link
                href={branch.mapUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1 text-primary hover:underline mt-2"
              >
                View on Google Maps <ExternalLink className="h-4 w-4" />
              </Link>
              {/* View Details Button */}
              <button
                className="mt-2 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition w-max"
                onClick={() => setModalIdx(idx)}
              >
                View Details
              </button>
            </div>
          ))}
        </div>
      </div>
      {/* Modal */}
      {modalIdx !== null && branches[modalIdx] && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-background rounded-xl shadow-xl max-w-lg w-full p-6 relative animate-fadeIn">
            <button
              className="absolute top-2 right-2 text-xl text-muted-foreground hover:text-primary"
              onClick={() => setModalIdx(null)}
              aria-label="Close modal"
            >
              &times;
            </button>
            <h3 className="text-2xl font-bold mb-2">
              {branches[modalIdx].name}
            </h3>
            <p className="text-muted-foreground mb-1">
              {branches[modalIdx].address}
            </p>
            {branches[modalIdx].note && (
              <p className="text-xs text-muted-foreground mb-2">
                {branches[modalIdx].note}
              </p>
            )}
            {(branches[modalIdx].email || branches[modalIdx].contact) && (
              <div className="flex gap-4 items-center mb-2">
                {branches[modalIdx].email && (
                  <span className="text-primary text-sm">
                    {branches[modalIdx].email}
                  </span>
                )}
                {branches[modalIdx].contact && (
                  <span className="text-primary text-sm">
                    {branches[modalIdx].contact}
                  </span>
                )}
                {branches[modalIdx].contactPerson && (
                  <span className="text-xs text-muted-foreground">
                    ({branches[modalIdx].contactPerson})
                  </span>
                )}
              </div>
            )}
            {/* Calendar */}
            <div className="mb-4">
              <h4 className="font-semibold mb-1">Operating Hours</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                {days.map((day) => {
                  const hours = branches[modalIdx].hours?.[day];
                  return (
                    <div key={day} className="flex justify-between">
                      <span className="font-medium">{day}</span>
                      <span>
                        {typeof hours === "string" && hours.length > 0 ? (
                          hours
                        ) : (
                          <span className="text-muted-foreground">Closed</span>
                        )}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
            {/* Meetings */}
            {branches[modalIdx].meetings &&
              branches[modalIdx].meetings.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-1">Meetings</h4>
                  <ul className="space-y-1">
                    {branches[modalIdx].meetings!.map((meeting, i) => (
                      <li key={i} className="border-b border-border pb-1 mb-1">
                        <span className="font-medium">{meeting.title}</span>{" "}
                        <span className="text-xs text-muted-foreground">
                          ({meeting.day}, {meeting.time})
                        </span>
                        {meeting.description && (
                          <div className="text-xs text-muted-foreground">
                            {meeting.description}
                          </div>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
          </div>
        </div>
      )}
    </section>
  );
}
