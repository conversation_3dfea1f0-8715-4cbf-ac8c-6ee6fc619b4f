// Mock data for when YouTube API quota is exceeded

export const mockLiveStream = {
  id: "UCjYkOcueiCATTTJ5FoaxylA",
  title: "Sunday Service - Live Stream from Times and Seasons Church",
  description:
    "Join us for our Sunday service live stream. Worship, prayer, and a powerful message from our pastor at Times and Seasons Church (Revival Fire Missions Int'l).",
  thumbnail: "/images/bg.jpg",
  publishedAt: new Date().toISOString(),
  channelTitle: "Times and Seasons Church (Revival Fire Missions Int'l)",
  liveBroadcastContent: "live" as const,
  wasLive: true,
};

export const mockSermons = [
  {
    id: "mock-sermon-1",
    title: "The Power of Faith - Sunday Service",
    description: "Discover how faith can move mountains in your life.",
    thumbnail: "/images/bg.jpg",
    publishedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week ago
    channelTitle: "Times and Seasons Church (Revival Fire Missions Int'l)",
    liveBroadcastContent: "none" as const,
    wasLive: true,
  },
  {
    id: "mock-sermon-2",
    title: "Walking in God's Purpose - Midweek Service",
    description:
      "Learn how to discover and fulfill God's purpose for your life.",
    thumbnail: "/images/bg.jpg",
    publishedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 2 weeks ago
    channelTitle: "Times and Seasons Church (Revival Fire Missions Int'l)",
    liveBroadcastContent: "none" as const,
    wasLive: true,
  },
  {
    id: "mock-sermon-3",
    title: "The Grace of God - Sunday Service",
    description: "Understanding God's unmerited favor in our lives.",
    thumbnail: "/images/bg.jpg",
    publishedAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(), // 3 weeks ago
    channelTitle: "Times and Seasons Church (Revival Fire Missions Int'l)",
    liveBroadcastContent: "none" as const,
    wasLive: false,
  },
  {
    id: "mock-sermon-4",
    title: "Overcoming Challenges - Special Service",
    description: "Biblical strategies for overcoming life's challenges.",
    thumbnail: "/images/bg.jpg",
    publishedAt: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000).toISOString(), // 4 weeks ago
    channelTitle: "Times and Seasons Church (Revival Fire Missions Int'l)",
    liveBroadcastContent: "none" as const,
    wasLive: false,
  },
  {
    id: "mock-sermon-5",
    title: "The Power of Prayer - Sunday Service",
    description:
      "Discover how prayer can transform your life and circumstances.",
    thumbnail: "/images/bg.jpg",
    publishedAt: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000).toISOString(), // 5 weeks ago
    channelTitle: "Times and Seasons Church (Revival Fire Missions Int'l)",
    liveBroadcastContent: "none" as const,
    wasLive: true,
  },
  {
    id: "mock-sermon-6",
    title: "Living a Life of Worship - Midweek Service",
    description: "How to make worship a lifestyle beyond Sunday services.",
    thumbnail: "/images/bg.jpg",
    publishedAt: new Date(Date.now() - 42 * 24 * 60 * 60 * 1000).toISOString(), // 6 weeks ago
    channelTitle: "Times and Seasons Church (Revival Fire Missions Int'l)",
    liveBroadcastContent: "none" as const,
    wasLive: false,
  },
  {
    id: "mock-sermon-7",
    title: "The Kingdom of God - Sunday Service",
    description:
      "Understanding the principles of God's Kingdom and how to apply them in your life.",
    thumbnail: "/images/bg.jpg",
    publishedAt: new Date(Date.now() - 49 * 24 * 60 * 60 * 1000).toISOString(), // 7 weeks ago
    channelTitle: "Times and Seasons Church (Revival Fire Missions Int'l)",
    liveBroadcastContent: "none" as const,
    wasLive: true,
  },
  {
    id: "mock-sermon-8",
    title: "Walking in Divine Favor - Special Service",
    description:
      "How to position yourself for God's favor in every area of your life.",
    thumbnail: "/images/bg.jpg",
    publishedAt: new Date(Date.now() - 56 * 24 * 60 * 60 * 1000).toISOString(), // 8 weeks ago
    channelTitle: "Times and Seasons Church (Revival Fire Missions Int'l)",
    liveBroadcastContent: "none" as const,
    wasLive: true,
  },
];
