/**
 * React hook for real-time live stream updates using Server-Sent Events
 * Provides live stream status, viewer counts, and health metrics in real-time
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { YouTubeVideo, LiveStreamAnalytics } from '@/lib/youtube-live';
import { LiveStreamEvent, StreamHealthMetrics } from '@/lib/youtube-realtime';

export interface RealtimeLiveStreamState {
  isLive: boolean;
  liveStream: YouTubeVideo | null;
  viewerCount: number;
  healthMetrics: StreamHealthMetrics | null;
  analytics: LiveStreamAnalytics | null;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  lastUpdate: string | null;
  error: string | null;
}

export interface RealtimeLiveStreamHook extends RealtimeLiveStreamState {
  // Actions
  reconnect: () => void;
  disconnect: () => void;
  
  // Event handlers (optional)
  onStreamStarted?: (stream: YouTubeVideo) => void;
  onStreamEnded?: (stream: YouTubeVideo | null) => void;
  onViewerCountChanged?: (newCount: number, oldCount: number) => void;
  onHealthChanged?: (health: string, metrics: StreamHealthMetrics) => void;
}

interface UseRealtimeLiveStreamOptions {
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  onStreamStarted?: (stream: YouTubeVideo) => void;
  onStreamEnded?: (stream: YouTubeVideo | null) => void;
  onViewerCountChanged?: (newCount: number, oldCount: number) => void;
  onHealthChanged?: (health: string, metrics: StreamHealthMetrics) => void;
}

export function useRealtimeLiveStream(
  options: UseRealtimeLiveStreamOptions = {}
): RealtimeLiveStreamHook {
  const {
    autoConnect = true,
    reconnectAttempts = 3,
    reconnectDelay = 5000,
    onStreamStarted,
    onStreamEnded,
    onViewerCountChanged,
    onHealthChanged
  } = options;

  // State
  const [state, setState] = useState<RealtimeLiveStreamState>({
    isLive: false,
    liveStream: null,
    viewerCount: 0,
    healthMetrics: null,
    analytics: null,
    connectionStatus: 'disconnected',
    lastUpdate: null,
    error: null
  });

  // Refs
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectCountRef = useRef(0);
  const mountedRef = useRef(true);

  // Update state helper
  const updateState = useCallback((updates: Partial<RealtimeLiveStreamState>) => {
    if (!mountedRef.current) return;
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Handle SSE events
  const handleEvent = useCallback((event: MessageEvent) => {
    if (!mountedRef.current) return;

    try {
      const data: LiveStreamEvent | any = JSON.parse(event.data);
      
      switch (data.type) {
        case 'connection_established':
          console.log('🔗 Real-time connection established');
          updateState({
            connectionStatus: 'connected',
            isLive: !!data.data.currentStream,
            liveStream: data.data.currentStream,
            viewerCount: data.data.currentStream?.concurrentViewers || 0,
            healthMetrics: data.data.healthMetrics,
            lastUpdate: data.timestamp,
            error: null
          });
          reconnectCountRef.current = 0;
          break;

        case 'stream_started':
          console.log('🔴 Stream started event received');
          const startedStream = data.data.stream;
          updateState({
            isLive: true,
            liveStream: startedStream,
            viewerCount: startedStream?.concurrentViewers || 0,
            analytics: data.data.analytics,
            lastUpdate: data.timestamp
          });
          onStreamStarted?.(startedStream);
          break;

        case 'stream_ended':
          console.log('⚫ Stream ended event received');
          const endedStream = state.liveStream;
          updateState({
            isLive: false,
            liveStream: null,
            viewerCount: 0,
            healthMetrics: null,
            analytics: data.data.analytics,
            lastUpdate: data.timestamp
          });
          onStreamEnded?.(endedStream);
          break;

        case 'viewer_count_updated':
          const newCount = data.data.viewerCount || 0;
          const oldCount = data.data.previousViewerCount || 0;
          updateState({
            liveStream: data.data.stream,
            viewerCount: newCount,
            lastUpdate: data.timestamp
          });
          onViewerCountChanged?.(newCount, oldCount);
          break;

        case 'stream_health_changed':
          const health = data.data.health;
          const metrics = data.data.analytics;
          updateState({
            healthMetrics: metrics,
            analytics: data.data.analytics,
            lastUpdate: data.timestamp
          });
          onHealthChanged?.(health, metrics);
          break;

        case 'heartbeat':
          // Silent heartbeat - just update last update time
          updateState({ lastUpdate: data.timestamp });
          break;

        case 'error':
          console.error('Real-time monitoring error:', data.data.message);
          updateState({
            error: data.data.message,
            lastUpdate: data.timestamp
          });
          break;

        default:
          console.log('Unknown event type:', data.type);
      }
    } catch (error) {
      console.error('Error parsing SSE event:', error);
      updateState({ error: 'Failed to parse real-time event' });
    }
  }, [updateState, onStreamStarted, onStreamEnded, onViewerCountChanged, onHealthChanged, state.liveStream]);

  // Connect to SSE
  const connect = useCallback(() => {
    if (eventSourceRef.current || !mountedRef.current) return;

    console.log('🔌 Connecting to real-time live stream events...');
    updateState({ connectionStatus: 'connecting', error: null });

    const eventSource = new EventSource('/api/live-stream-events');
    eventSourceRef.current = eventSource;

    eventSource.onopen = () => {
      if (!mountedRef.current) return;
      console.log('✅ Real-time connection opened');
      updateState({ connectionStatus: 'connected', error: null });
    };

    eventSource.onmessage = handleEvent;

    eventSource.onerror = (error) => {
      if (!mountedRef.current) return;
      console.error('❌ Real-time connection error:', error);
      
      updateState({ 
        connectionStatus: 'error',
        error: 'Connection to real-time updates failed'
      });

      // Attempt reconnection
      if (reconnectCountRef.current < reconnectAttempts) {
        reconnectCountRef.current++;
        console.log(`🔄 Attempting reconnection ${reconnectCountRef.current}/${reconnectAttempts} in ${reconnectDelay}ms`);
        
        reconnectTimeoutRef.current = setTimeout(() => {
          if (mountedRef.current) {
            disconnect();
            connect();
          }
        }, reconnectDelay);
      } else {
        console.log('❌ Max reconnection attempts reached');
        updateState({ connectionStatus: 'disconnected' });
      }
    };
  }, [updateState, handleEvent, reconnectAttempts, reconnectDelay]);

  // Disconnect from SSE
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (eventSourceRef.current) {
      console.log('🔌 Disconnecting from real-time events');
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    updateState({ connectionStatus: 'disconnected' });
  }, [updateState]);

  // Reconnect function
  const reconnect = useCallback(() => {
    reconnectCountRef.current = 0;
    disconnect();
    setTimeout(connect, 1000);
  }, [disconnect, connect]);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      mountedRef.current = false;
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return {
    ...state,
    reconnect,
    disconnect,
    onStreamStarted,
    onStreamEnded,
    onViewerCountChanged,
    onHealthChanged
  };
}
