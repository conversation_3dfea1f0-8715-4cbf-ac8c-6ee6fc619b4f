/**
 * Advanced Sermon Search Component
 * Provides search input, filters, suggestions, and results display
 */

'use client';

import React, { useState } from 'react';
import { Search, Filter, X, Calendar, User, Tag, SortAsc, SortDesc, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { useSermonSearch, SearchFilters } from '@/hooks/useSermonSearch';

interface SermonSearchProps {
  onResultsChange?: (results: any[]) => void;
  className?: string;
}

export function SermonSearch({ onResultsChange, className = '' }: SermonSearchProps) {
  const [showFilters, setShowFilters] = useState(false);
  const [tempQuery, setTempQuery] = useState('');

  const search = useSermonSearch({
    autoSearch: false,
    onSearchComplete: (results) => {
      onResultsChange?.(results.sermons);
    }
  });

  const handleSearch = () => {
    search.search(tempQuery);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setTempQuery(suggestion);
    search.search(suggestion);
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    search.setFilters({ [key]: value });
  };

  const clearAllFilters = () => {
    search.setFilters({
      dateRange: undefined,
      category: undefined,
      speaker: undefined,
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (search.filters.dateRange?.from || search.filters.dateRange?.to) count++;
    if (search.filters.category) count++;
    if (search.filters.speaker) count++;
    if (search.filters.sortBy !== 'relevance') count++;
    return count;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="relative flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search sermons by title, speaker, or topic..."
              value={tempQuery}
              onChange={(e) => setTempQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="pl-10 pr-4 h-12 text-base"
            />
            {tempQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setTempQuery('')}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          
          <Button
            onClick={handleSearch}
            disabled={search.isSearching}
            className="h-12 px-6"
          >
            {search.isSearching ? 'Searching...' : 'Search'}
          </Button>
          
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="h-12 px-4 relative"
          >
            <Filter className="h-4 w-4" />
            {getActiveFilterCount() > 0 && (
              <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs">
                {getActiveFilterCount()}
              </Badge>
            )}
          </Button>
        </div>

        {/* Search Suggestions */}
        {search.suggestions.length > 0 && tempQuery && (
          <Card className="absolute top-full left-0 right-0 z-10 mt-1">
            <CardContent className="p-2">
              <div className="text-xs text-muted-foreground mb-2">Suggestions:</div>
              <div className="flex flex-wrap gap-1">
                {search.suggestions.map((suggestion, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="h-6 px-2 text-xs"
                  >
                    {suggestion}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Advanced Filters */}
      {showFilters && (
        <Card>
          <CardContent className="p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Advanced Filters</h3>
              {search.hasFilters && (
                <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                  Clear All
                </Button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Date Range */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  Date Range
                </label>
                <div className="space-y-1">
                  <Input
                    type="date"
                    placeholder="From"
                    value={search.filters.dateRange?.from || ''}
                    onChange={(e) => handleFilterChange('dateRange', {
                      ...search.filters.dateRange,
                      from: e.target.value
                    })}
                    className="text-xs"
                  />
                  <Input
                    type="date"
                    placeholder="To"
                    value={search.filters.dateRange?.to || ''}
                    onChange={(e) => handleFilterChange('dateRange', {
                      ...search.filters.dateRange,
                      to: e.target.value
                    })}
                    className="text-xs"
                  />
                </div>
              </div>

              {/* Category */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-1">
                  <Tag className="h-3 w-3" />
                  Category
                </label>
                <select
                  value={search.filters.category || ''}
                  onChange={(e) => handleFilterChange('category', e.target.value || undefined)}
                  className="w-full p-2 border rounded-md text-sm"
                >
                  <option value="">All Categories</option>
                  <option value="conference">Conference</option>
                  <option value="sunday-service">Sunday Service</option>
                  <option value="teaching">Teaching</option>
                  <option value="prayer">Prayer</option>
                  <option value="healing">Healing</option>
                  <option value="testimony">Testimony</option>
                  <option value="special">Special Event</option>
                </select>
              </div>

              {/* Speaker */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-1">
                  <User className="h-3 w-3" />
                  Speaker
                </label>
                <Input
                  type="text"
                  placeholder="Speaker name..."
                  value={search.filters.speaker || ''}
                  onChange={(e) => handleFilterChange('speaker', e.target.value || undefined)}
                  className="text-sm"
                />
              </div>

              {/* Sort Options */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-1">
                  {search.filters.sortOrder === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />}
                  Sort By
                </label>
                <div className="space-y-1">
                  <select
                    value={search.filters.sortBy || 'relevance'}
                    onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                    className="w-full p-2 border rounded-md text-sm"
                  >
                    <option value="relevance">Relevance</option>
                    <option value="date">Date</option>
                    <option value="title">Title</option>
                  </select>
                  <select
                    value={search.filters.sortOrder || 'desc'}
                    onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
                    className="w-full p-2 border rounded-md text-sm"
                  >
                    <option value="desc">Descending</option>
                    <option value="asc">Ascending</option>
                  </select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Active Filters Display */}
      {search.hasFilters && (
        <div className="flex flex-wrap gap-2">
          {search.filters.dateRange?.from && (
            <Badge variant="secondary" className="gap-1">
              From: {new Date(search.filters.dateRange.from).toLocaleDateString()}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleFilterChange('dateRange', {
                  ...search.filters.dateRange,
                  from: undefined
                })}
              />
            </Badge>
          )}
          {search.filters.dateRange?.to && (
            <Badge variant="secondary" className="gap-1">
              To: {new Date(search.filters.dateRange.to).toLocaleDateString()}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleFilterChange('dateRange', {
                  ...search.filters.dateRange,
                  to: undefined
                })}
              />
            </Badge>
          )}
          {search.filters.category && (
            <Badge variant="secondary" className="gap-1">
              Category: {search.filters.category}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleFilterChange('category', undefined)}
              />
            </Badge>
          )}
          {search.filters.speaker && (
            <Badge variant="secondary" className="gap-1">
              Speaker: {search.filters.speaker}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleFilterChange('speaker', undefined)}
              />
            </Badge>
          )}
        </div>
      )}

      {/* Search Results Summary */}
      {search.hasSearched && (
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div>
            {search.hasResults ? (
              <>
                Found {search.totalResults.toLocaleString()} sermon{search.totalResults !== 1 ? 's' : ''}
                {search.query && ` for "${search.query}"`}
                <span className="ml-2 flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {search.searchTime}ms
                </span>
              </>
            ) : (
              <>
                No sermons found
                {search.query && ` for "${search.query}"`}
              </>
            )}
          </div>
          
          {search.hasSearched && (
            <Button variant="ghost" size="sm" onClick={search.clearSearch}>
              Clear Search
            </Button>
          )}
        </div>
      )}

      {/* Error Display */}
      {search.error && (
        <div className="bg-destructive/10 text-destructive p-3 rounded-lg text-sm">
          Search error: {search.error}
        </div>
      )}
    </div>
  );
}

export default SermonSearch;
