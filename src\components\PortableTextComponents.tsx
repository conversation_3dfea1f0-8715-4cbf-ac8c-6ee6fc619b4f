import Image from "next/image";
import Link from "next/link";
import { urlForImage } from "@/sanity/image";
import { ReactNode } from "react";

interface PortableTextProps {
  children: ReactNode;
}

interface PortableTextImageProps {
  value: {
    alt?: string;
    [key: string]: unknown;
  };
}

interface PortableTextLinkProps {
  children: ReactNode;
  value: {
    href: string;
    [key: string]: unknown;
  };
}

export const portableTextComponents = {
  block: {
    normal: ({ children }: PortableTextProps) => (
      <p className="mb-4">{children}</p>
    ),
    h1: ({ children }: PortableTextProps) => (
      <h1 className="text-3xl font-bold mt-8 mb-4">{children}</h1>
    ),
    h2: ({ children }: PortableTextProps) => (
      <h2 className="text-2xl font-bold mt-8 mb-4">{children}</h2>
    ),
    h3: ({ children }: PortableTextProps) => (
      <h3 className="text-xl font-bold mt-6 mb-3">{children}</h3>
    ),
    h4: ({ children }: PortableTextProps) => (
      <h4 className="text-lg font-bold mt-4 mb-2">{children}</h4>
    ),
    blockquote: ({ children }: PortableTextProps) => (
      <div className="my-6">
        <blockquote className="border-l-4 border-primary pl-4 italic">
          {children}
        </blockquote>
      </div>
    ),
  },
  types: {
    image: ({ value }: PortableTextImageProps) => {
      return (
        <div className="relative w-full h-96 my-8 rounded-lg overflow-hidden">
          <Image
            src={urlForImage(value).url()}
            alt={value.alt || "Blog post image"}
            fill
            className="object-cover"
          />
        </div>
      );
    },
  },
  marks: {
    link: ({ children, value }: PortableTextLinkProps) => {
      const rel = !value.href.startsWith("/")
        ? "noreferrer noopener"
        : undefined;
      const target = !value.href.startsWith("/") ? "_blank" : undefined;

      return (
        <Link
          href={value.href}
          rel={rel}
          target={target}
          className="text-primary underline hover:text-primary/80 transition-colors"
        >
          {children}
        </Link>
      );
    },
  },
  list: {
    bullet: ({ children }: PortableTextProps) => (
      <ul className="list-disc pl-6 my-4">{children}</ul>
    ),
    number: ({ children }: PortableTextProps) => (
      <ol className="list-decimal pl-6 my-4">{children}</ol>
    ),
  },
};
