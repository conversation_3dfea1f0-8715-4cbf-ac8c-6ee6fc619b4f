import { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";
import { notFound } from "next/navigation";

import { getClient } from "@/sanity/client";
import { postBySlugQuery } from "@/sanity/queries";
import { urlForImage } from "@/sanity/image";
import { PortableText } from "@portabletext/react";
import { portableTextComponents } from "@/components/PortableTextComponents";
import { format } from "date-fns";
import { ArrowLeft } from "lucide-react";
import { generateBlogMetadata } from "@/lib/seo";
import { BlogSEO } from "@/components/BlogSEO";

type PostPageProps = {
  params: {
    slug: string;
  };
  searchParams?: { [key: string]: string | string[] | undefined };
};

export const revalidate = 3600; // Revalidate the data at most every hour

export async function generateMetadata({
  params,
}: PostPageProps): Promise<Metadata> {
  const post = await getClient().fetch(postBySlugQuery, { slug: params.slug });

  if (!post) {
    return {
      title: "Post Not Found | Revival Fire Missions",
      description: "The requested blog post could not be found.",
      robots: { index: false, follow: false },
    };
  }

  return generateBlogMetadata({
    title: post.title,
    description:
      post.excerpt ||
      `${post.title} - Read this article on the Revival Fire Missions blog`,
    image: post.mainImage,
    publishedAt: post.publishedAt,
    author: post.author,
    slug: params.slug,
  });
}

export default async function BlogPostPage({ params }: PostPageProps) {
  const post = await getClient().fetch(postBySlugQuery, { slug: params.slug });

  if (!post) {
    notFound();
  }

  return (
    <main className="min-h-screen">
      {/* Add structured data for SEO */}
      <BlogSEO
        title={post.title}
        description={
          post.excerpt ||
          `${post.title} - Read this article on the Revival Fire Missions blog`
        }
        publishedAt={post.publishedAt}
        image={post.mainImage}
        author={post.author}
        url={`${process.env.NEXT_PUBLIC_BASE_URL || "https://revivalfiremissions.org"}/blog/${params.slug}`}
      />

      <article className="py-20 bg-secondary/30">
        <div className="container mx-auto px-4">
          <Link
            href="/blog"
            className="inline-flex items-center text-primary hover:text-primary/80 mb-8"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to all posts
          </Link>

          <div className="max-w-3xl mx-auto">
            <div className="mb-8">
              <div className="flex flex-wrap gap-2 mb-4">
                {post.categories?.map((category: string) => (
                  <span
                    key={category}
                    className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full"
                  >
                    {category}
                  </span>
                ))}
              </div>

              <h1 className="text-4xl font-bold mb-4">{post.title}</h1>

              <div className="flex items-center justify-between text-muted-foreground mb-6">
                <div className="flex items-center gap-2">
                  {post.author?.image && (
                    <div className="relative w-8 h-8 rounded-full overflow-hidden">
                      <Image
                        src={urlForImage(post.author.image)
                          .width(96)
                          .height(96)
                          .url()}
                        alt={post.author.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  <span>{post.author?.name || "Unknown author"}</span>
                </div>

                <div className="text-sm">
                  {post.publishedAt ? (
                    <time dateTime={post.publishedAt}>
                      {format(new Date(post.publishedAt), "MMMM d, yyyy")}
                    </time>
                  ) : (
                    "Date unknown"
                  )}
                  <span className="mx-2">•</span>
                  <span>{post.estimatedReadingTime || 3} min read</span>
                </div>
              </div>
            </div>

            {post.mainImage && (
              <div className="relative aspect-video mb-10 rounded-xl overflow-hidden shadow-lg">
                <Image
                  src={urlForImage(post.mainImage)
                    .width(1200)
                    .height(675)
                    .url()}
                  alt={post.title}
                  fill
                  className="object-cover"
                  priority
                />
              </div>
            )}

            <div className="prose prose-lg dark:prose-invert max-w-none">
              <PortableText
                value={post.body}
                components={portableTextComponents}
              />
            </div>
          </div>
        </div>
      </article>
    </main>
  );
}
