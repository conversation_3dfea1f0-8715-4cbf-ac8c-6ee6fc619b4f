/**
 * Advanced Sermon Search API
 * Supports text search, date filtering, category filtering, and sorting
 */

import { NextRequest } from 'next/server';
import { YouTubeVideo } from '@/lib/youtube-live';

// Search result interface
interface SearchResult {
  sermons: YouTubeVideo[];
  totalResults: number;
  totalPages: number;
  currentPage: number;
  searchQuery: string;
  filters: SearchFilters;
  suggestions: string[];
  searchTime: number;
}

interface SearchFilters {
  dateRange?: {
    from?: string;
    to?: string;
  };
  sortBy?: 'relevance' | 'date' | 'title' | 'duration';
  sortOrder?: 'asc' | 'desc';
  category?: string;
  speaker?: string;
  minDuration?: number;
  maxDuration?: number;
}

// Common sermon keywords for suggestions
const SERMON_KEYWORDS = [
  'faith', 'prayer', 'worship', 'blessing', 'salvation', 'grace', 'love', 'hope',
  'peace', 'joy', 'healing', 'miracle', 'testimony', 'bible', 'scripture', 'gospel',
  'jesus', 'christ', 'god', 'holy spirit', 'church', 'ministry', 'apostle', 'pastor',
  'conference', 'service', 'sunday', 'theme', 'year', 'passover', 'revival', 'fire',
  'missions', 'international', 'collins', 'chipaya', 'patrick', 'nyaga'
];

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const { searchParams } = new URL(request.url);
    
    // Extract search parameters
    const query = searchParams.get('q') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const sortBy = searchParams.get('sortBy') as SearchFilters['sortBy'] || 'relevance';
    const sortOrder = searchParams.get('sortOrder') as SearchFilters['sortOrder'] || 'desc';
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const category = searchParams.get('category');
    const speaker = searchParams.get('speaker');
    const minDuration = searchParams.get('minDuration') ? parseInt(searchParams.get('minDuration')!) : undefined;
    const maxDuration = searchParams.get('maxDuration') ? parseInt(searchParams.get('maxDuration')!) : undefined;
    const debug = searchParams.get('debug') === 'true';

    console.log(`🔍 Sermon search request: "${query}" | Page: ${page} | Sort: ${sortBy} ${sortOrder}`);

    // Get all sermons from the main API
    const baseUrl = request.url.replace('/api/search-sermons', '/api/live-status');
    const sermonsResponse = await fetch(baseUrl, {
      headers: {
        'User-Agent': request.headers.get('User-Agent') || 'Search-API',
      },
    });

    if (!sermonsResponse.ok) {
      throw new Error(`Failed to fetch sermons: ${sermonsResponse.status}`);
    }

    const sermonsData = await sermonsResponse.json();
    let allSermons: YouTubeVideo[] = sermonsData.sermons || [];

    console.log(`📚 Total sermons available for search: ${allSermons.length}`);

    // Apply text search
    let filteredSermons = allSermons;
    if (query.trim()) {
      const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);
      
      filteredSermons = allSermons.filter(sermon => {
        const searchableText = [
          sermon.title,
          sermon.description,
          sermon.channelTitle,
        ].join(' ').toLowerCase();

        // Check if all search terms are present
        return searchTerms.every(term => searchableText.includes(term));
      });

      console.log(`🎯 Text search filtered to ${filteredSermons.length} sermons`);
    }

    // Apply date range filter
    if (dateFrom || dateTo) {
      filteredSermons = filteredSermons.filter(sermon => {
        const sermonDate = new Date(sermon.publishedAt);
        
        if (dateFrom && sermonDate < new Date(dateFrom)) return false;
        if (dateTo && sermonDate > new Date(dateTo)) return false;
        
        return true;
      });
      
      console.log(`📅 Date filter applied: ${filteredSermons.length} sermons remaining`);
    }

    // Apply speaker filter
    if (speaker) {
      filteredSermons = filteredSermons.filter(sermon => 
        sermon.title.toLowerCase().includes(speaker.toLowerCase()) ||
        sermon.description.toLowerCase().includes(speaker.toLowerCase())
      );
      
      console.log(`🎤 Speaker filter applied: ${filteredSermons.length} sermons remaining`);
    }

    // Apply category filter (based on title keywords)
    if (category) {
      const categoryKeywords = getCategoryKeywords(category);
      filteredSermons = filteredSermons.filter(sermon => 
        categoryKeywords.some(keyword => 
          sermon.title.toLowerCase().includes(keyword.toLowerCase())
        )
      );
      
      console.log(`🏷️ Category filter applied: ${filteredSermons.length} sermons remaining`);
    }

    // Calculate relevance scores for sorting
    if (query.trim() && sortBy === 'relevance') {
      const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);
      
      filteredSermons = filteredSermons.map(sermon => ({
        ...sermon,
        relevanceScore: calculateRelevanceScore(sermon, searchTerms)
      }));
    }

    // Apply sorting
    filteredSermons.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'relevance':
          comparison = ((b as any).relevanceScore || 0) - ((a as any).relevanceScore || 0);
          break;
        case 'date':
          comparison = new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
          break;
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        default:
          comparison = new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
      }
      
      return sortOrder === 'asc' ? -comparison : comparison;
    });

    // Generate search suggestions
    const suggestions = generateSearchSuggestions(query, allSermons);

    // Apply pagination
    const totalResults = filteredSermons.length;
    const totalPages = Math.ceil(totalResults / limit);
    const startIndex = (page - 1) * limit;
    const paginatedSermons = filteredSermons.slice(startIndex, startIndex + limit);

    // Remove relevance scores from final results
    const cleanSermons = paginatedSermons.map(sermon => {
      const { relevanceScore, ...cleanSermon } = sermon as any;
      return cleanSermon;
    });

    const searchTime = Date.now() - startTime;

    const result: SearchResult = {
      sermons: cleanSermons,
      totalResults,
      totalPages,
      currentPage: page,
      searchQuery: query,
      filters: {
        dateRange: dateFrom || dateTo ? { from: dateFrom || undefined, to: dateTo || undefined } : undefined,
        sortBy,
        sortOrder,
        category: category || undefined,
        speaker: speaker || undefined,
        minDuration,
        maxDuration,
      },
      suggestions,
      searchTime,
    };

    console.log(`✅ Search completed in ${searchTime}ms: ${totalResults} results, ${totalPages} pages`);

    // Add debug information if requested
    if (debug) {
      (result as any).debug = {
        totalSermons: allSermons.length,
        filteredSermons: totalResults,
        searchTerms: query.split(' ').filter(term => term.length > 0),
        appliedFilters: {
          hasTextSearch: !!query.trim(),
          hasDateFilter: !!(dateFrom || dateTo),
          hasSpeakerFilter: !!speaker,
          hasCategoryFilter: !!category,
        },
        searchTime,
        serverTime: new Date().toISOString(),
      };
    }

    return Response.json(result);

  } catch (error) {
    console.error('❌ Search error:', error);
    
    const errorResult: Partial<SearchResult> = {
      sermons: [],
      totalResults: 0,
      totalPages: 0,
      currentPage: 1,
      searchQuery: '',
      filters: {},
      suggestions: [],
      searchTime: Date.now() - startTime,
    };

    return Response.json(
      {
        ...errorResult,
        error: error instanceof Error ? error.message : 'Search failed',
      },
      { status: 500 }
    );
  }
}

// Calculate relevance score for search results
function calculateRelevanceScore(sermon: YouTubeVideo, searchTerms: string[]): number {
  let score = 0;
  const title = sermon.title.toLowerCase();
  const description = sermon.description.toLowerCase();
  
  searchTerms.forEach(term => {
    // Title matches are worth more
    if (title.includes(term)) {
      score += title.startsWith(term) ? 10 : 5; // Boost for title start matches
    }
    
    // Description matches
    if (description.includes(term)) {
      score += 2;
    }
    
    // Exact phrase matches get bonus
    if (title.includes(searchTerms.join(' '))) {
      score += 15;
    }
  });
  
  // Boost recent sermons slightly
  const daysOld = (Date.now() - new Date(sermon.publishedAt).getTime()) / (1000 * 60 * 60 * 24);
  if (daysOld < 30) score += 1;
  
  return score;
}

// Generate search suggestions based on query and available sermons
function generateSearchSuggestions(query: string, sermons: YouTubeVideo[]): string[] {
  if (!query.trim()) return SERMON_KEYWORDS.slice(0, 5);
  
  const queryLower = query.toLowerCase();
  const suggestions = new Set<string>();
  
  // Find keywords that start with the query
  SERMON_KEYWORDS.forEach(keyword => {
    if (keyword.toLowerCase().startsWith(queryLower) && keyword !== queryLower) {
      suggestions.add(keyword);
    }
  });
  
  // Find common words from sermon titles
  sermons.forEach(sermon => {
    const words = sermon.title.toLowerCase().split(/\s+/);
    words.forEach(word => {
      if (word.length > 3 && word.startsWith(queryLower) && word !== queryLower) {
        suggestions.add(word);
      }
    });
  });
  
  return Array.from(suggestions).slice(0, 8);
}

// Get keywords for category filtering
function getCategoryKeywords(category: string): string[] {
  const categoryMap: Record<string, string[]> = {
    'conference': ['conference', 'passover', 'apostolic'],
    'sunday-service': ['sunday', 'service', 'worship'],
    'teaching': ['teaching', 'bible', 'scripture', 'word'],
    'prayer': ['prayer', 'intercession', 'spiritual'],
    'healing': ['healing', 'miracle', 'deliverance'],
    'testimony': ['testimony', 'witness', 'experience'],
    'special': ['special', 'celebration', 'event'],
  };
  
  return categoryMap[category.toLowerCase()] || [category];
}
