/**
 * YouTube Embed URL Generator
 * Creates YouTube embed URLs with proper parameters to prevent showing videos from other channels
 */

export interface YouTubeEmbedOptions {
  autoplay?: boolean;
  controls?: boolean;
  modestbranding?: boolean;
  showinfo?: boolean;
  rel?: boolean;
  fs?: boolean;
  cc_load_policy?: boolean;
  iv_load_policy?: number;
  autohide?: boolean;
  start?: number;
  end?: number;
  loop?: boolean;
  playlist?: string;
  mute?: boolean;
}

/**
 * Generates a YouTube embed URL with parameters optimized to prevent showing other channels' videos
 * @param videoId - The YouTube video ID
 * @param options - Optional parameters to customize the embed
 * @returns The complete YouTube embed URL
 */
export function generateYouTubeEmbedUrl(
  videoId: string,
  options: YouTubeEmbedOptions = {}
): string {
  const {
    autoplay = false,
    controls = true,
    modestbranding = true,
    showinfo = false,
    rel = false, // CRITICAL: This prevents showing related videos from other channels
    fs = true,
    cc_load_policy = false,
    iv_load_policy = 3, // Hide video annotations
    autohide = true,
    start,
    end,
    loop = false,
    playlist,
    mute = false,
  } = options;

  const params = new URLSearchParams();

  // Core parameters to prevent other channels' content
  params.set('rel', rel ? '1' : '0'); // 0 = only show related videos from same channel
  params.set('modestbranding', modestbranding ? '1' : '0'); // Remove YouTube branding
  params.set('showinfo', showinfo ? '1' : '0'); // Hide video info overlay
  params.set('iv_load_policy', iv_load_policy.toString()); // Hide annotations

  // Playback parameters
  params.set('autoplay', autoplay ? '1' : '0');
  params.set('controls', controls ? '1' : '0');
  params.set('fs', fs ? '1' : '0'); // Allow fullscreen
  params.set('cc_load_policy', cc_load_policy ? '1' : '0'); // Closed captions
  params.set('autohide', autohide ? '1' : '0'); // Auto-hide controls

  // Optional parameters
  if (start !== undefined) {
    params.set('start', start.toString());
  }
  if (end !== undefined) {
    params.set('end', end.toString());
  }
  if (loop) {
    params.set('loop', '1');
    // For loop to work, we need to set the playlist to the same video
    params.set('playlist', playlist || videoId);
  }
  if (mute) {
    params.set('mute', '1');
  }

  return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;
}

/**
 * Generates a YouTube embed URL specifically for live streams
 * @param videoId - The YouTube video ID
 * @param autoplay - Whether to autoplay the video
 * @returns The complete YouTube embed URL optimized for live streams
 */
export function generateLiveStreamEmbedUrl(
  videoId: string,
  autoplay: boolean = true
): string {
  return generateYouTubeEmbedUrl(videoId, {
    autoplay,
    controls: true,
    modestbranding: true,
    showinfo: false,
    rel: false, // CRITICAL: Prevent other channels' videos
    fs: true,
    cc_load_policy: false,
    iv_load_policy: 3,
    autohide: true,
  });
}

/**
 * Generates a YouTube embed URL specifically for past sermons/videos
 * @param videoId - The YouTube video ID
 * @param autoplay - Whether to autoplay the video
 * @returns The complete YouTube embed URL optimized for past videos
 */
export function generateSermonEmbedUrl(
  videoId: string,
  autoplay: boolean = true
): string {
  return generateYouTubeEmbedUrl(videoId, {
    autoplay,
    controls: true,
    modestbranding: true,
    showinfo: false,
    rel: false, // CRITICAL: Prevent other channels' videos
    fs: true,
    cc_load_policy: false,
    iv_load_policy: 3,
    autohide: true,
  });
}

/**
 * Default embed URL for church videos - prevents all external suggestions
 * @param videoId - The YouTube video ID
 * @param autoplay - Whether to autoplay the video
 * @returns The complete YouTube embed URL with maximum privacy settings
 */
export function generateChurchVideoEmbedUrl(
  videoId: string,
  autoplay: boolean = true
): string {
  return generateYouTubeEmbedUrl(videoId, {
    autoplay,
    controls: true,
    modestbranding: true, // Remove YouTube branding
    showinfo: false, // Hide video title and uploader info
    rel: false, // CRITICAL: Only show related videos from same channel
    fs: true, // Allow fullscreen
    cc_load_policy: false, // Don't force closed captions
    iv_load_policy: 3, // Hide video annotations
    autohide: true, // Auto-hide player controls
  });
}

/**
 * YouTube embed parameters explanation:
 * 
 * rel=0: MOST IMPORTANT - When set to 0, related videos will come from the same channel as the video that was just played.
 * modestbranding=1: Removes most YouTube branding from the player
 * showinfo=0: Hides video title and uploader info before video starts
 * controls=1: Shows player controls
 * fs=1: Allows fullscreen
 * cc_load_policy=0: Doesn't force closed captions
 * iv_load_policy=3: Hides video annotations
 * autohide=1: Auto-hides player controls after a few seconds
 * autoplay=1: Automatically starts playing the video
 */
