import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/ThemeProvider";
import { Footer } from "@/components/Footer";
import { Navbar } from "@/components/Navbar";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_BASE_URL || "https://revivalfiremissions.org"
  ),
  title: "Times and Seasons Church | Revival Fire Missions Int'l",
  description:
    "Times and Seasons Church (Revival Fire Missions Int'l) is dedicated to spreading the Gospel and transforming lives through the power of God's love.",
  keywords: [
    "church",
    "times and seasons",
    "revival",
    "fire missions",
    "sermons",
    "gospel",
    "christian",
    "ministry",
    "lusaka",
    "zambia",
  ],
  authors: [{ name: "Times and Seasons Church (Revival Fire Missions Int'l)" }],
  creator: "Times and Seasons Church",
  publisher: "Revival Fire Missions Int'l",
  icons: {
    icon: "logo/logo transparent[64].png",
    apple: "logo/logo transparent[64].png",
    shortcut: "logo/logo transparent[64].png",
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://revivalfiremissions.org",
    siteName: "Times and Seasons Church",
    title: "Times and Seasons Church | Revival Fire Missions Int'l",
    description:
      "Times and Seasons Church (Revival Fire Missions Int'l) is dedicated to spreading the Gospel and transforming lives through the power of God's love.",
    images: [
      {
        url: "logo/logo transparent[64].png",
        width: 64,
        height: 64,
        alt: "Times and Seasons Church (Revival Fire Missions Int'l) Logo",
      },
      {
        url: "images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Times and Seasons Church (Revival Fire Missions Int'l)",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Times and Seasons Church | Revival Fire Missions Int'l",
    description:
      "Times and Seasons Church (Revival Fire Missions Int'l) is dedicated to spreading the Gospel and transforming lives through the power of God's love.",
    images: ["logo/logo transparent[64].png", "images/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth" suppressHydrationWarning={true}>
      <head>
        <link rel="icon" href="/logo/logo transparent[64].png" sizes="any" />
        <link rel="apple-touch-icon" href="/logo/logo transparent[64].png" />
        <meta name="theme-color" content="#3b82f6" />
        <meta
          property="og:image"
          content="https://revivalfiremissions.org/logo/logo transparent[64].png"
        />
        <meta property="og:image:width" content="64" />
        <meta property="og:image:height" content="64" />
        <meta property="og:image:type" content="image/png" />
        <meta name="application-name" content="Times and Seasons Church" />
        <meta
          name="msapplication-TileImage"
          content="/logo/logo transparent[64].png"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <main className="relative min-h-screen overflow-hidden">
            <Navbar />
            {children}
            <Footer />
          </main>
        </ThemeProvider>
      </body>
    </html>
  );
}
