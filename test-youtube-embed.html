<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Embed Test - Times and Seasons Church</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            margin: 20px 0;
        }
        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 8px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        .test-section.good {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .test-section.bad {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        h1, h2 {
            color: #333;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
            margin-bottom: 10px;
        }
        .status.good {
            background-color: #28a745;
            color: white;
        }
        .status.bad {
            background-color: #dc3545;
            color: white;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .checklist {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 YouTube Embed Test - Times and Seasons Church</h1>
        <p>This page tests the YouTube embed fix to ensure no suggestions from other channels appear.</p>
        
        <div class="instructions">
            <h3>📋 Testing Instructions:</h3>
            <ol>
                <li><strong>Play the video below</strong> (it should start automatically)</li>
                <li><strong>Pause the video</strong> and check for suggestions</li>
                <li><strong>Let the video finish</strong> and check end screen</li>
                <li><strong>Look for the differences</strong> between the two players</li>
            </ol>
        </div>
    </div>

    <div class="comparison">
        <div class="container test-section good">
            <div class="status good">✅ FIXED VERSION</div>
            <h2>With Channel Filtering (rel=0)</h2>
            <p><strong>Expected behavior:</strong> No suggestions from other channels, clean interface</p>
            
            <div class="video-container">
                <iframe
                    src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=0&rel=0&modestbranding=1&showinfo=0&controls=1&fs=1&cc_load_policy=0&iv_load_policy=3&autohide=1"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen
                    title="Fixed YouTube Embed - No External Suggestions">
                </iframe>
            </div>
            
            <div class="checklist">
                <h4>✅ What you should see:</h4>
                <ul>
                    <li>Clean player interface when paused</li>
                    <li>No suggestions from other channels</li>
                    <li>Minimal YouTube branding</li>
                    <li>No video annotations</li>
                    <li>Professional appearance</li>
                </ul>
            </div>
        </div>

        <div class="container test-section bad">
            <div class="status bad">❌ OLD VERSION</div>
            <h2>Without Channel Filtering (default)</h2>
            <p><strong>Problem behavior:</strong> Shows suggestions from any channel</p>
            
            <div class="video-container">
                <iframe
                    src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen
                    title="Old YouTube Embed - Shows External Suggestions">
                </iframe>
            </div>
            
            <div class="checklist">
                <h4>❌ Problems with this version:</h4>
                <ul>
                    <li>Shows suggestions from other channels</li>
                    <li>Distracting overlays and branding</li>
                    <li>Video annotations may appear</li>
                    <li>Less professional appearance</li>
                    <li>Users may click away to other content</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🔧 Technical Implementation</h2>
        <p>The fix uses these YouTube embed parameters:</p>
        
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;">
            <strong>Fixed URL:</strong><br>
            https://www.youtube.com/embed/VIDEO_ID?<br>
            &nbsp;&nbsp;autoplay=0<br>
            &nbsp;&nbsp;<strong>&rel=0</strong> ← CRITICAL: Only same channel suggestions<br>
            &nbsp;&nbsp;&modestbranding=1 ← Remove YouTube branding<br>
            &nbsp;&nbsp;&showinfo=0 ← Hide video info overlay<br>
            &nbsp;&nbsp;&controls=1 ← Show player controls<br>
            &nbsp;&nbsp;&fs=1 ← Allow fullscreen<br>
            &nbsp;&nbsp;&cc_load_policy=0 ← Don't force captions<br>
            &nbsp;&nbsp;&iv_load_policy=3 ← Hide annotations<br>
            &nbsp;&nbsp;&autohide=1 ← Auto-hide controls
        </div>

        <div class="instructions">
            <h3>🎯 For Your Church Website:</h3>
            <p>The fix has been applied to your sermon video players. When users watch your church videos:</p>
            <ul>
                <li>✅ They will only see suggestions from your channel (if any)</li>
                <li>✅ No distracting content from other channels</li>
                <li>✅ Clean, professional video player appearance</li>
                <li>✅ Better user experience and engagement</li>
            </ul>
        </div>

        <div class="checklist">
            <h3>📝 Next Steps:</h3>
            <ol>
                <li><strong>Test on your website:</strong> Play a video and check the behavior</li>
                <li><strong>Verify on different devices:</strong> Desktop, mobile, tablet</li>
                <li><strong>Check different browsers:</strong> Chrome, Firefox, Safari, Edge</li>
                <li><strong>Monitor user engagement:</strong> Users should stay on your content longer</li>
            </ol>
        </div>
    </div>

    <script>
        // Add some interactivity to highlight the differences
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎥 YouTube Embed Test Page Loaded');
            console.log('✅ Fixed version: rel=0 prevents external suggestions');
            console.log('❌ Old version: Shows suggestions from any channel');
            
            // Log when videos are interacted with
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach((iframe, index) => {
                iframe.addEventListener('load', function() {
                    const type = index === 0 ? 'FIXED' : 'OLD';
                    console.log(`📺 ${type} video player loaded`);
                });
            });
        });
    </script>
</body>
</html>
