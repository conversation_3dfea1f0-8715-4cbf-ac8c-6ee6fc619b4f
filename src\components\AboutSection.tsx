"use client";

import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { useEffect, useState } from "react";
import Link from "next/link";

interface SlideImage {
  url: string;
  alt: string;
}

interface AboutData {
  title: string;
  mainContent: string;
  secondaryContent?: string;
  values: string[];
  slideImages: SlideImage[];
  primaryButtonText?: string;
  primaryButtonLink?: string;
  secondaryButtonText?: string;
  secondaryButtonLink?: string;
}

export function AboutSection() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [aboutData, setAboutData] = useState<AboutData | null>(null);
  const [loading, setLoading] = useState(true);
  // Error state for handling fetch failures
  const [, setError] = useState<string | null>(null);

  // Fallback data in case Sanity data is not available
  const fallbackData: AboutData = {
    title: "About Revival Fire Missions",
    mainContent:
      "Revival Fire Missions is a dynamic church dedicated to spreading the Gospel and transforming lives through the power of God's love. Founded on the principles of faith, hope, and love, we strive to create a welcoming community where everyone can experience God's presence.",
    secondaryContent:
      "Our mission is to ignite hearts and transform lives by sharing the message of Jesus Christ. We believe in the power of prayer, worship, and community to bring about lasting change in individuals and society.",
    values: [
      "Faith in Action",
      "Compassionate Community",
      "Biblical Teaching",
      "Spirit-Led Worship",
    ],
    slideImages: Array.from({ length: 10 }, (_, i) => ({
      url: `/images/slides/${i + 1}.jpg`,
      alt: `Revival Fire Missions Church - Slide ${i + 1}`,
    })),
    primaryButtonText: "Our Vision",
    secondaryButtonText: "Learn More",
  };

  useEffect(() => {
    async function fetchAboutData() {
      try {
        setLoading(true);
        const response = await fetch("/api/about");

        if (!response.ok) {
          throw new Error("Failed to fetch about data");
        }

        const data = await response.json();
        setAboutData(data);
      } catch (err) {
        console.error("Error fetching about data:", err);
        setError("Failed to load about section data");
        // Use fallback data if fetch fails
        setAboutData(fallbackData);
      } finally {
        setLoading(false);
      }
    }

    fetchAboutData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Use data from Sanity or fallback to hardcoded data
  const data = aboutData || fallbackData;
  const totalSlides = data.slideImages.length;

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % totalSlides);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(interval);
  }, [totalSlides]);
  // Show loading state
  if (loading) {
    return (
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4 text-center">
          <div className="animate-pulse">
            <div className="h-8 bg-secondary/50 rounded w-1/3 mx-auto mb-6"></div>
            <div className="h-4 bg-secondary/50 rounded w-2/3 mx-auto mb-4"></div>
            <div className="h-4 bg-secondary/50 rounded w-2/3 mx-auto mb-4"></div>
            <div className="h-4 bg-secondary/50 rounded w-1/2 mx-auto mb-8"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="order-2 lg:order-1">
            <h2 className="text-4xl font-bold mb-6 text-foreground">
              {data.title}
            </h2>
            <p className="text-lg text-muted-foreground mb-6">
              {data.mainContent}
            </p>
            {data.secondaryContent && (
              <p className="text-lg text-muted-foreground mb-8">
                {data.secondaryContent}
              </p>
            )}
            <div className="flex flex-wrap gap-4">
              {data.primaryButtonText && (
                <Button
                  size="lg"
                  {...(data.primaryButtonLink
                    ? {
                        asChild: true,
                      }
                    : {})}
                >
                  {data.primaryButtonLink ? (
                    <Link href={data.primaryButtonLink}>
                      {data.primaryButtonText}
                    </Link>
                  ) : (
                    data.primaryButtonText
                  )}
                </Button>
              )}
              {data.secondaryButtonText && (
                <Button
                  variant="outline"
                  size="lg"
                  {...(data.secondaryButtonLink
                    ? {
                        asChild: true,
                      }
                    : {})}
                >
                  {data.secondaryButtonLink ? (
                    <Link href={data.secondaryButtonLink}>
                      {data.secondaryButtonText}
                    </Link>
                  ) : (
                    data.secondaryButtonText
                  )}
                </Button>
              )}
            </div>
          </div>
          <div className="order-1 lg:order-2 relative">
            <div className="relative h-[400px] lg:h-[500px] rounded-xl overflow-hidden shadow-xl">
              {data.slideImages.map((slide, index) => (
                <div
                  key={index}
                  className={`absolute inset-0 transition-opacity duration-1000 ${
                    currentSlide === index ? "opacity-100" : "opacity-0"
                  }`}
                >
                  <Image
                    src={slide.url}
                    alt={slide.alt || `Slide ${index + 1}`}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 50vw"
                    priority={index === 0}
                  />
                </div>
              ))}
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>

              {/* Slide indicators */}
              <div className="absolute bottom-4 right-4 flex space-x-2">
                {data.slideImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-2 h-2 rounded-full transition-all ${
                      currentSlide === index ? "bg-primary w-4" : "bg-white/50"
                    }`}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>
            </div>
            <div className="absolute bottom-4 left-4 bg-background/90 backdrop-blur-sm p-4 rounded-lg shadow-lg max-w-xs border border-border">
              <p className="font-semibold text-primary">Our Values</p>
              <ul className="mt-2 space-y-1 text-sm text-foreground">
                {data.values.map((value, index) => (
                  <li key={index}>• {value}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
